from typing import List
from modules.common.schema import Chunk
from modules.chunks.IChunk import IChunk
from backend.python.modules.common.constants import LanguageEnum


class BaseLineChunk(IChunk):
    def chunk_file(self, file_path: str, file_content: str, language: LanguageEnum = LanguageEnum.TEXT, window_size: int = 50, overflow_size: int = 10) -> List[Chunk]:
        # 移除代码中的空白行
        lines = file_content.splitlines()
        
        chunks = []
        # 按照windo_size和overflow_size进行分块
        for i in range(0, len(lines), window_size - overflow_size):
            chunks.append(
                Chunk(
                    file_path=file_path,
                    start_line=i,
                    end_line=i + window_size,
                    content="\n".join(lines[i : i + window_size]),
                )
            )
            
        return chunks
    