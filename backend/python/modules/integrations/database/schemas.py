from pydantic import BaseModel, Field, ConfigDict

class RepoFileChunk:
    """仓库文件分块结构"""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    chunk_id: int = Field(..., description="分块ID")
    
    file_path: str = Field(..., description="文件路径")
    start_line: int = Field(..., description="起始行号")
    end_line: int = Field(..., description="结束行号")
    content: str = Field(..., description="代码内容")

class RepoFile:
    """仓库文件结构"""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    file_path: str = Field(..., description="文件路径")
    
    file_key_lines: str = Field(..., description="文件关键行")

    file_hash: str = Field(..., description="文件哈希")

    updated_at: int = Field(..., description="更新时间戳")


class Repo:
    """仓库结构"""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    repo_path: str = Field(..., description="仓库路径")

    updated_at: int = Field(..., description="更新时间戳")

    term_sparse: str = Field(..., description="BM25json序列化字符串")

    inverted_index: str = Field(..., description="关键结构json序列化字符串")

    updated_at: int = Field(..., description="更新时间戳")

