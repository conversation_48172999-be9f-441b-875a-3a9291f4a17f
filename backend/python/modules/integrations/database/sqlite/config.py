from pydantic import BaseModel, Field
from typing import Optional
import os

class SQLiteConfig(BaseModel):
    """SQLite数据库配置"""
    
    db_path: str = Field(default="data/codebase.db", description="数据库文件路径")
    timeout: float = Field(default=30.0, description="连接超时时间(秒)")
    check_same_thread: bool = Field(default=False, description="是否检查同一线程")
    isolation_level: Optional[str] = Field(default=None, description="事务隔离级别")
    
    def get_absolute_db_path(self, base_dir: Optional[str] = None) -> str:
        """获取数据库文件的绝对路径"""
        if os.path.isabs(self.db_path):
            return self.db_path
        
        if base_dir is None:
            base_dir = os.getcwd()
        
        return os.path.join(base_dir, self.db_path)
    
    def ensure_db_directory(self, base_dir: Optional[str] = None) -> None:
        """确保数据库目录存在"""
        db_path = self.get_absolute_db_path(base_dir)
        db_dir = os.path.dirname(db_path)
        
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)

# 默认配置实例
default_sqlite_config = SQLiteConfig()
