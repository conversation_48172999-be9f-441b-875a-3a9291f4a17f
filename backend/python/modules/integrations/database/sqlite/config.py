from pydantic import BaseModel, Field
from typing import Optional
import os
import sys
from pathlib import Path

# 添加core目录到Python路径以导入Config基类
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent.parent / "core"))

try:
    from config import Config as BaseConfig
    _has_base_config = True
except ImportError:
    # 如果无法导入基础Config，则使用BaseModel
    BaseConfig = BaseModel
    _has_base_config = False

class SQLiteConfig(BaseModel):
    """SQLite数据库配置

    这个类可以独立使用，也可以作为主配置系统的一部分。
    如果需要与主配置系统集成，可以通过组合的方式使用。
    """

    db_path: str = Field(default="data/codebase.db", description="数据库文件路径")
    timeout: float = Field(default=30.0, description="连接超时时间(秒)")
    check_same_thread: bool = Field(default=False, description="是否检查同一线程")
    isolation_level: Optional[str] = Field(default=None, description="事务隔离级别")
    
    def get_absolute_db_path(self, base_dir: Optional[str] = None) -> str:
        """获取数据库文件的绝对路径"""
        if os.path.isabs(self.db_path):
            return self.db_path
        
        if base_dir is None:
            base_dir = os.getcwd()
        
        return os.path.join(base_dir, self.db_path)
    
    def ensure_db_directory(self, base_dir: Optional[str] = None) -> None:
        """确保数据库目录存在"""
        db_path = self.get_absolute_db_path(base_dir)
        db_dir = os.path.dirname(db_path)

        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)

class ExtendedConfig(BaseConfig if _has_base_config else BaseModel):
    """扩展的配置类，包含SQLite配置

    这个类继承自主配置系统的Config基类，并添加了SQLite配置。
    只有在能够导入主配置系统时才会创建这个类。
    """

    sqlite: SQLiteConfig = Field(default_factory=SQLiteConfig, description="SQLite数据库配置")

# 默认配置实例
default_sqlite_config = SQLiteConfig()

# 如果可以导入主配置系统，则提供扩展配置
if _has_base_config:
    __all__ = ["SQLiteConfig", "ExtendedConfig", "default_sqlite_config"]
else:
    __all__ = ["SQLiteConfig", "default_sqlite_config"]
