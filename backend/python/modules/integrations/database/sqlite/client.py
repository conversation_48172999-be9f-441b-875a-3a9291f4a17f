import os
import sqlite3
from typing import List, Optional, Dict, Any
from contextlib import contextmanager
import logging
from pathlib import Path

from modules.integrations.database.schemas import Repo, RepoFile, RepoFileChunk
from core.config import Config, SQLiteConfig, get_config

logger = logging.getLogger(__name__)

class SQLiteClient:
    """SQLite数据库客户端"""
    
    def __init__(self, config: Config = get_config()):
        """初始化SQLite客户端
        
        Args:
            config: SQLite配置，如果为None则使用默认配置
        """
        self.config = config.database.sqlite
        self._ensure_database_setup()
    
    def _ensure_database_setup(self) -> None:
        """确保数据库和表结构存在"""

        if not os.path.exists(self.config.db_path):
            os.makedirs(os.path.dirname(self.config.db_path), exist_ok=True)
            with self.get_connection() as conn:
                self._create_tables(conn)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(
            self.config.db_path,
            timeout=self.config.timeout,
            check_same_thread=self.config.check_same_thread,
            isolation_level=self.config.isolation_level
        )
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        try:
            yield conn
        except Exception as e:
            conn.rollback()
            logger.error(f"Database operation failed: {e}")
            raise
        finally:
            conn.close()
    
    def _create_tables(self, conn: sqlite3.Connection) -> None:
        """创建数据库表"""
        cursor = conn.cursor()
        
        # 创建仓库表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS repos (
                repo_path TEXT PRIMARY KEY,
                updated_at INTEGER NOT NULL,
                term_sparse TEXT NOT NULL,
                inverted_index TEXT NOT NULL
            )
        """)
        
        # 创建文件表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS repo_files (
                file_path TEXT PRIMARY KEY,
                file_key_lines TEXT NOT NULL,
                file_hash TEXT NOT NULL,
                updated_at INTEGER NOT NULL
            )
        """)
        
        # 创建文件分块表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS repo_file_chunks (
                chunk_id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_path TEXT NOT NULL,
                start_line INTEGER NOT NULL,
                end_line INTEGER NOT NULL,
                content TEXT NOT NULL,
                FOREIGN KEY (file_path) REFERENCES repo_files (file_path) ON DELETE CASCADE
            )
        """)
        
        # 创建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_chunks_file_path ON repo_file_chunks(file_path)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_files_updated_at ON repo_files(updated_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_repos_updated_at ON repos(updated_at)")
        
        conn.commit()
    
    # 仓库相关操作
    def insert_repo(self, repo: Repo) -> None:
        """插入或更新仓库信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO repos (repo_path, updated_at, term_sparse, inverted_index)
                VALUES (?, ?, ?, ?)
            """, (repo.repo_path, repo.updated_at, repo.term_sparse, repo.inverted_index))
            conn.commit()
    
    def get_repo(self, repo_path: str) -> Optional[Repo]:
        """根据路径获取仓库信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM repos WHERE repo_path = ?", (repo_path,))
            row = cursor.fetchone()
            
            if row:
                return Repo(
                    repo_path=row['repo_path'],
                    updated_at=row['updated_at'],
                    term_sparse=row['term_sparse'],
                    inverted_index=row['inverted_index']
                )
            return None
    
    def get_all_repos(self) -> List[Repo]:
        """获取所有仓库信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM repos ORDER BY updated_at DESC")
            rows = cursor.fetchall()
            
            return [
                Repo(
                    repo_path=row['repo_path'],
                    updated_at=row['updated_at'],
                    term_sparse=row['term_sparse'],
                    inverted_index=row['inverted_index']
                )
                for row in rows
            ]
    
    def delete_repo(self, repo_path: str) -> bool:
        """删除仓库信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM repos WHERE repo_path = ?", (repo_path,))
            conn.commit()
            return cursor.rowcount > 0

    # 文件相关操作
    def insert_file(self, file: RepoFile) -> None:
        """插入或更新文件信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO repo_files (file_path, file_key_lines, file_hash, updated_at)
                VALUES (?, ?, ?, ?)
            """, (file.file_path, file.file_key_lines, file.file_hash, file.updated_at))
            conn.commit()

    def get_file(self, file_path: str) -> Optional[RepoFile]:
        """根据路径获取文件信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM repo_files WHERE file_path = ?", (file_path,))
            row = cursor.fetchone()

            if row:
                return RepoFile(
                    file_path=row['file_path'],
                    file_key_lines=row['file_key_lines'],
                    file_hash=row['file_hash'],
                    updated_at=row['updated_at']
                )
            return None

    def get_files_by_pattern(self, pattern: str) -> List[RepoFile]:
        """根据路径模式获取文件列表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM repo_files WHERE file_path LIKE ? ORDER BY file_path", (pattern,))
            rows = cursor.fetchall()

            return [
                RepoFile(
                    file_path=row['file_path'],
                    file_key_lines=row['file_key_lines'],
                    file_hash=row['file_hash'],
                    updated_at=row['updated_at']
                )
                for row in rows
            ]

    def get_all_files(self) -> List[RepoFile]:
        """获取所有文件信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM repo_files ORDER BY file_path")
            rows = cursor.fetchall()

            return [
                RepoFile(
                    file_path=row['file_path'],
                    file_key_lines=row['file_key_lines'],
                    file_hash=row['file_hash'],
                    updated_at=row['updated_at']
                )
                for row in rows
            ]

    def delete_file(self, file_path: str) -> bool:
        """删除文件信息（会级联删除相关的分块）"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM repo_files WHERE file_path = ?", (file_path,))
            conn.commit()
            return cursor.rowcount > 0

    # 文件分块相关操作
    def insert_chunk(self, chunk: RepoFileChunk) -> int:
        """插入文件分块，返回分块ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO repo_file_chunks (file_path, start_line, end_line, content)
                VALUES (?, ?, ?, ?)
            """, (chunk.file_path, chunk.start_line, chunk.end_line, chunk.content))
            conn.commit()
            return cursor.lastrowid

    def insert_chunks(self, chunks: List[RepoFileChunk]) -> List[int]:
        """批量插入文件分块，返回分块ID列表"""
        chunk_ids = []
        with self.get_connection() as conn:
            cursor = conn.cursor()
            for chunk in chunks:
                cursor.execute("""
                    INSERT INTO repo_file_chunks (file_path, start_line, end_line, content)
                    VALUES (?, ?, ?, ?)
                """, (chunk.file_path, chunk.start_line, chunk.end_line, chunk.content))
                chunk_ids.append(cursor.lastrowid)
            conn.commit()
        return chunk_ids

    def get_chunk(self, chunk_id: int) -> Optional[RepoFileChunk]:
        """根据ID获取文件分块"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM repo_file_chunks WHERE chunk_id = ?", (chunk_id,))
            row = cursor.fetchone()

            if row:
                return RepoFileChunk(
                    chunk_id=row['chunk_id'],
                    file_path=row['file_path'],
                    start_line=row['start_line'],
                    end_line=row['end_line'],
                    content=row['content']
                )
            return None

    def get_chunks_by_file(self, file_path: str) -> List[RepoFileChunk]:
        """根据文件路径获取所有分块"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM repo_file_chunks
                WHERE file_path = ?
                ORDER BY start_line
            """, (file_path,))
            rows = cursor.fetchall()

            return [
                RepoFileChunk(
                    chunk_id=row['chunk_id'],
                    file_path=row['file_path'],
                    start_line=row['start_line'],
                    end_line=row['end_line'],
                    content=row['content']
                )
                for row in rows
            ]

    def get_chunks_by_line_range(self, file_path: str, start_line: int, end_line: int) -> List[RepoFileChunk]:
        """根据文件路径和行号范围获取分块"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM repo_file_chunks
                WHERE file_path = ? AND start_line <= ? AND end_line >= ?
                ORDER BY start_line
            """, (file_path, end_line, start_line))
            rows = cursor.fetchall()

            return [
                RepoFileChunk(
                    chunk_id=row['chunk_id'],
                    file_path=row['file_path'],
                    start_line=row['start_line'],
                    end_line=row['end_line'],
                    content=row['content']
                )
                for row in rows
            ]

    def delete_chunk(self, chunk_id: int) -> bool:
        """删除指定分块"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM repo_file_chunks WHERE chunk_id = ?", (chunk_id,))
            conn.commit()
            return cursor.rowcount > 0

    def delete_chunks_by_file(self, file_path: str) -> int:
        """删除指定文件的所有分块，返回删除的数量"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM repo_file_chunks WHERE file_path = ?", (file_path,))
            conn.commit()
            return cursor.rowcount

    # 统计和查询方法
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 统计各表的记录数
            cursor.execute("SELECT COUNT(*) as count FROM repos")
            repo_count = cursor.fetchone()['count']

            cursor.execute("SELECT COUNT(*) as count FROM repo_files")
            file_count = cursor.fetchone()['count']

            cursor.execute("SELECT COUNT(*) as count FROM repo_file_chunks")
            chunk_count = cursor.fetchone()['count']

            # 获取数据库文件大小
            db_size = Path(self.config.db_path).stat().st_size if Path(self.config.db_path).exists() else 0

            return {
                "repo_count": repo_count,
                "file_count": file_count,
                "chunk_count": chunk_count,
                "database_size_bytes": db_size,
                "database_path": self.config.db_path
            }

    def search_content(self, query: str, limit: int = 100) -> List[RepoFileChunk]:
        """在分块内容中搜索文本"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM repo_file_chunks
                WHERE content LIKE ?
                ORDER BY file_path, start_line
                LIMIT ?
            """, (f"%{query}%", limit))
            rows = cursor.fetchall()

            return [
                RepoFileChunk(
                    chunk_id=row['chunk_id'],
                    file_path=row['file_path'],
                    start_line=row['start_line'],
                    end_line=row['end_line'],
                    content=row['content']
                )
                for row in rows
            ]

    def clear_all_data(self) -> None:
        """清空所有数据（保留表结构）"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM repo_file_chunks")
            cursor.execute("DELETE FROM repo_files")
            cursor.execute("DELETE FROM repos")
            conn.commit()

    def close(self) -> None:
        """关闭客户端（当前实现中无需特殊处理）"""
        pass
