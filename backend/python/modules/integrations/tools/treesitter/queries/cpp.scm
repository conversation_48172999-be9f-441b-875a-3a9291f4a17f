; C++ Tree-sitter 查询模式
; 支持的 C++ 结构：
; - struct/class/union 声明
; - 函数/方法声明
; - typedef 声明
; - enum 声明
; - namespace 定义
; - template 声明
; - 宏定义
; - 变量声明
; - 构造函数/析构函数
; - 运算符重载
; - friend 声明
; - using 声明

; 命名空间定义 - 最高优先级
; 匹配样例: namespace MyNamespace { class MyClass {}; }
(namespace_definition
  name: (namespace_identifier) @name.definition.namespace) @definition.namespace

; 类声明
; 匹配样例: class MyClass { public: int x; };
(class_specifier
  name: (type_identifier) @name.definition.class) @definition.class

; 结构体声明
; 匹配样例: struct MyStruct { int x; };
(struct_specifier
  name: (type_identifier) @name.definition.class) @definition.class

; 联合体声明
; 匹配样例: union MyUnion { int x; float y; };
(union_specifier
  name: (type_identifier) @name.definition.class) @definition.class

; 枚举声明
; 匹配样例: enum Color { RED, GREEN, BLUE };
(enum_specifier
  name: (type_identifier) @name.definition.enum) @definition.enum

; 模板声明
; 匹配样例: template<typename T> class MyTemplate { T data; };
(template_declaration
  parameters: (template_parameter_list)
  (class_specifier
    name: (type_identifier) @name.definition.template.class)) @definition.template

; 析构函数声明 - 高优先级，避免与其他函数冲突
; 匹配样例: class MyClass { ~MyClass() { } };
(function_definition
  declarator: (function_declarator
    declarator: (destructor_name) @name.definition.destructor)) @definition.destructor

; 运算符重载 - 高优先级
; 匹配样例: class MyClass { MyClass operator+(const MyClass& other) { } };
(function_definition
  declarator: (function_declarator
    declarator: (operator_name) @name.definition.operator)) @definition.operator

; 方法定义（类成员函数）- 有返回类型的方法，优先匹配
; 匹配样例: class MyClass { void method() { } };
(function_definition
  type: (_)
  declarator: (function_declarator
    declarator: (field_identifier) @name.definition.method)) @definition.method

; 全局函数定义（带函数体）- 使用 identifier，有返回类型
; 匹配样例: int add(int a, int b) { return a + b; }
(function_definition
  type: (_)
  declarator: (function_declarator
    declarator: (identifier) @name.definition.function)) @definition.function

; 全局函数声明（原型）
; 匹配样例: int add(int a, int b);
(declaration
  type: (_)
  declarator: (function_declarator
    declarator: (identifier) @name.definition.function)) @definition.function

; 构造函数定义 - 没有返回类型，使用 identifier，最后匹配
; 匹配样例: class MyClass { MyClass() { } };
; 注意：这个规则会匹配所有没有返回类型的函数，包括一些全局函数
(function_definition
  declarator: (function_declarator
    declarator: (identifier) @name.definition.constructor)) @definition.constructor

; 类型定义
; 匹配样例: typedef int MyInt;
(type_definition
  type: (_)
  declarator: (type_identifier) @name.definition.type) @definition.type

; 宏定义
; 匹配样例: #define MAX(a, b) ((a) > (b) ? (a) : (b))
(preproc_function_def
  name: (identifier) @name.definition.macro) @definition.macro

; 友元声明
; 匹配样例: class MyClass { friend class OtherClass; };
(friend_declaration) @definition.friend

; using 声明
; 匹配样例: using std::cout;
(using_declaration) @definition.using
