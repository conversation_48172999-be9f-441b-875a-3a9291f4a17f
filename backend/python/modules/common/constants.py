from enum import Enum


class LanguageEnum(Enum):
    JAVA = ("java", ".java")
    PYTHON = ("python", ".py")
    C = ("c", ".c")
    CPP = ("cpp", ".cpp")
    GO = ("go", ".go")
    RUST = ("rust", ".rs")
    PHP = ("php", ".php")
    RUBY = ("ruby", ".rb")
    SWIFT = ("swift", ".swift")
    KOTLIN = ("kotlin", ".kt")
    SCALA = ("scala", ".scala")
    HTML = ("html", ".html")
    CSS = ("css", ".css")
    JAVASCRIPT = ("javascript", ".js")
    TYPESCRIPT = ("typescript", ".ts")
    JSX = ("jsx", ".jsx")
    TSX = ("tsx", ".tsx")
    JSON = ("json", ".json")
    XML = ("xml", ".xml")
    YAML = ("yaml", ".yaml")
    YML = ("yml", ".yml")
    TOML = ("toml", ".toml")
    MD = ("markdown", ".md")
    SQL = ("sql", ".sql")
    SH = ("sh", ".sh")
    BASH = ("bash", ".bash")
    ZSH = ("zsh", ".zsh")
    FISH = ("fish", ".fish")
    DOCKERFILE = ("dockerfile", ".dockerfile")
    MAKEFILE = ("makefile", ".makefile")

    @classmethod
    def from_suffix(cls, suffix: str):
        for lang in cls:
            if lang.value[1] == suffix:
                return lang
        return None

class FileFilterMode(Enum):
    LOCAL = "local"
    EMBEDDING = "embedding"

class SearchToolEnum(Enum):
    GREP = "grep"
    EMBEDDING = "embedding"
    INVERTED_INDEX = "inverted_index"
    TERM_SPRSE = "term_sparse"
    ANY = "any"

class IOToolEnum(Enum):
    FILE = "file_io"
    DIRECTORY = "directory_io"

