FileParser 解析结果报告 - SWIFT
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/swift/sample.swift
  文件名: sample.swift
  内容长度: 8711 字符
  行数: 337

关键结构行 (34 个):
  第   7 行: definition.interface: protocol UserRepositoryProtocol {
           内容: protocol UserRepositoryProtocol {
  第  13 行: definition.interface: protocol Validatable {
           内容: protocol Validatable {
  第  19 行: definition.class: enum UserStatus: String, CaseIterable {
           内容: enum UserStatus: String, CaseIterable {
  第  39 行: definition.class: enum NetworkError: Error {
           内容: enum NetworkError: Error {
  第  47 行: definition.class: struct User: Codable, Validatable {
           内容: struct User: Codable, Validatable {
  第  77 行: definition.method: mutating func activate() {
           内容: mutating func activate() {
  第  82 行: definition.method: mutating func updateName(_ newName: String) {
           内容: mutating func updateName(_ newName: String) {
  第  88 行: definition.method: func validate() -> [String] {
           内容: func validate() -> [String] {
  第 108 行: definition.method: private func isValidEmail(_ email: String) -> Bool {
           内容: private func isValidEmail(_ email: String) -> Bool {
  第 116 行: definition.class: class UserRepository: UserRepositoryProtocol {
           内容: class UserRepository: UserRepositoryProtocol {
  第 120 行: definition.method: func findUser(by id: Int) -> User? {
           内容: func findUser(by id: Int) -> User? {
  第 126 行: definition.method: func saveUser(_ user: User) -> Bool {
           内容: func saveUser(_ user: User) -> Bool {
  第 135 行: definition.method: func deleteUser(by id: Int) -> Bool {
           内容: func deleteUser(by id: Int) -> Bool {
  第 141 行: definition.method: func getAllUsers() -> [User] {
           内容: func getAllUsers() -> [User] {
  第 148 行: definition.class: class UserService {
           内容: class UserService {
  第 157 行: definition.method: func createUser(name: String, email: String) -> Result<User, NetworkError> {
           内容: func createUser(name: String, email: String) -> Result<User, NetworkError> {
  第 175 行: definition.method: func activateUser(id: Int) -> Bool {
           内容: func activateUser(id: Int) -> Bool {
  第 185 行: definition.method: private func generateUserId() -> Int {
           内容: private func generateUserId() -> Int {
  第 191 行: definition.class: class Repository<T: Codable> {
           内容: class Repository<T: Codable> {
  第 195 行: definition.method: func save(_ item: T, with key: String) {
           内容: func save(_ item: T, with key: String) {
  第 201 行: definition.method: func find(by key: String) -> T? {
           内容: func find(by key: String) -> T? {
  第 207 行: definition.method: func delete(by key: String) -> Bool {
           内容: func delete(by key: String) -> Bool {
  第 213 行: definition.method: func all() -> [T] {
           内容: func all() -> [T] {
  第 222 行: definition.method: static func createSampleUser() -> User {
           内容: static func createSampleUser() -> User {
  第 226 行: definition.method: func toJSON() -> String? {
           内容: func toJSON() -> String? {
  第 251 行: definition.class: class Logger {
           内容: class Logger {
  第 252 行: definition.class: enum Level: String {
           内容: enum Level: String {
  第 258 行: definition.method: func info(_ message: String) {
           内容: func info(_ message: String) {
  第 262 行: definition.method: func warning(_ message: String) {
           内容: func warning(_ message: String) {
  第 266 行: definition.method: func error(_ message: String) {
           内容: func error(_ message: String) {
  第 270 行: definition.method: private func log(_ message: String, level: Level) {
           内容: private func log(_ message: String, level: Level) {
  第 277 行: definition.method: func validateEmail(_ email: String) -> Bool {
           内容: func validateEmail(_ email: String) -> Bool {
  第 283 行: definition.method: func processUsers<T: Sequence>(_ users: T, transform: (User) -> Void) where T.Element == User {
           内容: func processUsers<T: Sequence>(_ users: T, transform: (User) -> Void) where T.Element == User {
  第 297 行: definition.method: func exampleUsage() {
           内容: func exampleUsage() {

检测到的结构类型:
  - definition.class: class Logger {: 1 个
  - definition.class: class Repository<T: Codable> {: 1 个
  - definition.class: class UserRepository: UserRepositoryProtocol {: 1 个
  - definition.class: class UserService {: 1 个
  - definition.class: enum Level: String {: 1 个
  - definition.class: enum NetworkError: Error {: 1 个
  - definition.class: enum UserStatus: String, CaseIterable {: 1 个
  - definition.class: struct User: Codable, Validatable {: 1 个
  - definition.interface: protocol UserRepositoryProtocol {: 1 个
  - definition.interface: protocol Validatable {: 1 个
  - definition.method: func activateUser(id: Int) -> Bool {: 1 个
  - definition.method: func all() -> [T] {: 1 个
  - definition.method: func createUser(name: String, email: String) -> Result<User, NetworkError> {: 1 个
  - definition.method: func delete(by key: String) -> Bool {: 1 个
  - definition.method: func deleteUser(by id: Int) -> Bool {: 1 个
  - definition.method: func error(_ message: String) {: 1 个
  - definition.method: func exampleUsage() {: 1 个
  - definition.method: func find(by key: String) -> T? {: 1 个
  - definition.method: func findUser(by id: Int) -> User? {: 1 个
  - definition.method: func getAllUsers() -> [User] {: 1 个
  - definition.method: func info(_ message: String) {: 1 个
  - definition.method: func processUsers<T: Sequence>(_ users: T, transform: (User) -> Void) where T.Element == User {: 1 个
  - definition.method: func save(_ item: T, with key: String) {: 1 个
  - definition.method: func saveUser(_ user: User) -> Bool {: 1 个
  - definition.method: func toJSON() -> String? {: 1 个
  - definition.method: func validate() -> [String] {: 1 个
  - definition.method: func validateEmail(_ email: String) -> Bool {: 1 个
  - definition.method: func warning(_ message: String) {: 1 个
  - definition.method: mutating func activate() {: 1 个
  - definition.method: mutating func updateName(_ newName: String) {: 1 个
  - definition.method: private func generateUserId() -> Int {: 1 个
  - definition.method: private func isValidEmail(_ email: String) -> Bool {: 1 个
  - definition.method: private func log(_ message: String, level: Level) {: 1 个
  - definition.method: static func createSampleUser() -> User {: 1 个

代码块信息 (5 个):
  块 1: 第 7-44 行 (38 行)
      7: protocol UserRepositoryProtocol {
      8:     func findUser(by id: Int) -> User?
      9:     func saveUser(_ user: User) -> Bool
    ... (还有 35 行)

  块 2: 第 47-146 行 (100 行)
     47: struct User: Codable, Validatable {
     48:     let id: Int
     49:     var name: String
    ... (还有 97 行)

  块 3: 第 148-218 行 (71 行)
    148: class UserService {
    149:     private let repository: UserRepositoryProtocol
    150:     private let logger: Logger
    ... (还有 68 行)

  块 4: 第 222-274 行 (53 行)
    222:     static func createSampleUser() -> User {
    223:         return User(id: 1, name: "John Doe", email: "<EMAIL>")
    224:     }
    ... (还有 50 行)

  块 5: 第 277-331 行 (55 行)
    277: func validateEmail(_ email: String) -> Bool {
    278:     let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
    279:     let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
    ... (还有 52 行)


统计信息:
  覆盖率: 94.1%
  块中总行数: 317
  结构类型数: 34
