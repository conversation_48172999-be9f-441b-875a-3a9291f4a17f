FileParser 解析结果报告 - PHP
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php
  文件名: sample.php
  内容长度: 13449 字符
  行数: 541

关键结构行 (58 个):
  第  31 行: definition.method: public function getLabel(): string
           内容: public function getLabel(): string
  第  43 行: definition.interface: interface UserRepositoryInterface
           内容: interface UserRepositoryInterface
  第  45 行: definition.method: public function findById(int $id): ?User;
           内容: public function findById(int $id): ?User;
  第  46 行: definition.method: public function save(User $user): bool;
           内容: public function save(User $user): bool;
  第  47 行: definition.method: public function delete(int $id): bool;
           内容: public function delete(int $id): bool;
  第  48 行: definition.method: public function findAll(): array;
           内容: public function findAll(): array;
  第  51 行: definition.interface: interface LoggerInterface
           内容: interface LoggerInterface
  第  53 行: definition.method: public function info(string $message, array $context = []): void;
           内容: public function info(string $message, array $context = []): void;
  第  54 行: definition.method: public function error(string $message, array $context = []): void;
           内容: public function error(string $message, array $context = []): void;
  第  63 行: definition.method: public function getCreatedAt(): DateTime
           内容: public function getCreatedAt(): DateTime
  第  68 行: definition.method: public function getUpdatedAt(): DateTime
           内容: public function getUpdatedAt(): DateTime
  第  73 行: definition.method: public function touch(): void
           内容: public function touch(): void
  第  81 行: definition.method: abstract public function validate(): array;
           内容: abstract public function validate(): array;
  第  83 行: definition.method: public function isValid(): bool
           内容: public function isValid(): bool
  第  90 行: definition.class: abstract class BaseModel implements JsonSerializable
           内容: abstract class BaseModel implements JsonSerializable
  第  96 行: definition.method: public function __construct()
           内容: public function __construct()
  第 102 行: definition.method: public function getId(): int
           内容: public function getId(): int
  第 107 行: definition.method: public function setId(int $id): void
           内容: public function setId(int $id): void
  第 112 行: definition.method: abstract public function toArray(): array;
           内容: abstract public function toArray(): array;
  第 114 行: definition.method: public function jsonSerialize(): array
           内容: public function jsonSerialize(): array
  第 121 行: definition.class: class User extends BaseModel
           内容: class User extends BaseModel
  第 132 行: definition.method: public function __construct(
           内容: public function __construct(
  第 145 行: definition.method: public function getName(): string
           内容: public function getName(): string
  第 150 行: definition.method: public function getEmail(): string
           内容: public function getEmail(): string
  第 155 行: definition.method: public function getStatus(): UserStatus
           内容: public function getStatus(): UserStatus
  第 160 行: definition.method: public function getRoles(): array
           内容: public function getRoles(): array
  第 165 行: definition.method: public function getAvatar(): ?string
           内容: public function getAvatar(): ?string
  第 171 行: definition.method: public function setName(string $name): self
           内容: public function setName(string $name): self
  第 178 行: definition.method: public function setEmail(string $email): self
           内容: public function setEmail(string $email): self
  第 185 行: definition.method: public function setStatus(UserStatus $status): self
           内容: public function setStatus(UserStatus $status): self
  第 192 行: definition.method: public function setAvatar(?string $avatar): self
           内容: public function setAvatar(?string $avatar): self
  第 200 行: definition.method: public function addRole(string $role): self
           内容: public function addRole(string $role): self
  第 209 行: definition.method: public function removeRole(string $role): self
           内容: public function removeRole(string $role): self
  第 216 行: definition.method: public function hasRole(string $role): bool
           内容: public function hasRole(string $role): bool
  第 222 行: definition.method: public function isActive(): bool
           内容: public function isActive(): bool
  第 227 行: definition.method: public function activate(): self
           内容: public function activate(): self
  第 232 行: definition.method: public function suspend(): self
           内容: public function suspend(): self
  第 238 行: definition.method: public function validate(): array
           内容: public function validate(): array
  第 256 行: definition.method: public function toArray(): array
           内容: public function toArray(): array
  第 271 行: definition.method: public function __toString(): string
           内容: public function __toString(): string
  第 276 行: definition.method: public function __clone()
           内容: public function __clone()
  第 285 行: definition.class: class UserRepository implements UserRepositoryInterface
           内容: class UserRepository implements UserRepositoryInterface
  第 290 行: definition.method: public function __construct(PDO $pdo, LoggerInterface $logger)
           内容: public function __construct(PDO $pdo, LoggerInterface $logger)
  第 296 行: definition.method: public function findById(int $id): ?User
           内容: public function findById(int $id): ?User
  第 317 行: definition.method: public function save(User $user): bool
           内容: public function save(User $user): bool
  第 334 行: definition.method: public function delete(int $id): bool
           内容: public function delete(int $id): bool
  第 351 行: definition.method: public function findAll(): array
           内容: public function findAll(): array
  第 370 行: definition.method: private function insert(User $user): bool
           内容: private function insert(User $user): bool
  第 393 行: definition.method: private function update(User $user): bool
           内容: private function update(User $user): bool
  第 411 行: definition.method: private function hydrate(array $data): User
           内容: private function hydrate(array $data): User
  第 433 行: definition.class: class UserService
           内容: class UserService
  第 438 行: definition.method: public function __construct(
           内容: public function __construct(
  第 446 行: definition.method: public function createUser(string $name, string $email): ?User
           内容: public function createUser(string $name, string $email): ?User
  第 465 行: definition.method: public function activateUser(int $id): bool
           内容: public function activateUser(int $id): bool
  第 480 行: definition.function: function generateRandomString(int $length = 10): string
           内容: function generateRandomString(int $length = 10): string
  第 493 行: definition.function: function validateEmail(string $email): bool
           内容: function validateEmail(string $email): bool
  第 517 行: definition.method: public function info(string $message, array $context = []): void {
           内容: public function info(string $message, array $context = []): void {
  第 521 行: definition.method: public function error(string $message, array $context = []): void {
           内容: public function error(string $message, array $context = []): void {

检测到的结构类型:
  - definition.class: abstract class BaseModel implements JsonSerializable: 1 个
  - definition.class: class User extends BaseModel: 1 个
  - definition.class: class UserRepository implements UserRepositoryInterface: 1 个
  - definition.class: class UserService: 1 个
  - definition.function: function generateRandomString(int $length = 10): string: 1 个
  - definition.function: function validateEmail(string $email): bool: 1 个
  - definition.interface: interface LoggerInterface: 1 个
  - definition.interface: interface UserRepositoryInterface: 1 个
  - definition.method: abstract public function toArray(): array;: 1 个
  - definition.method: abstract public function validate(): array;: 1 个
  - definition.method: private function hydrate(array $data): User: 1 个
  - definition.method: private function insert(User $user): bool: 1 个
  - definition.method: private function update(User $user): bool: 1 个
  - definition.method: public function __clone(): 1 个
  - definition.method: public function __construct(: 2 个
  - definition.method: public function __construct(): 1 个
  - definition.method: public function __construct(PDO $pdo, LoggerInterface $logger): 1 个
  - definition.method: public function __toString(): string: 1 个
  - definition.method: public function activate(): self: 1 个
  - definition.method: public function activateUser(int $id): bool: 1 个
  - definition.method: public function addRole(string $role): self: 1 个
  - definition.method: public function createUser(string $name, string $email): ?User: 1 个
  - definition.method: public function delete(int $id): bool: 1 个
  - definition.method: public function delete(int $id): bool;: 1 个
  - definition.method: public function error(string $message, array $context = []): void {: 1 个
  - definition.method: public function error(string $message, array $context = []): void;: 1 个
  - definition.method: public function findAll(): array: 1 个
  - definition.method: public function findAll(): array;: 1 个
  - definition.method: public function findById(int $id): ?User: 1 个
  - definition.method: public function findById(int $id): ?User;: 1 个
  - definition.method: public function getAvatar(): ?string: 1 个
  - definition.method: public function getCreatedAt(): DateTime: 1 个
  - definition.method: public function getEmail(): string: 1 个
  - definition.method: public function getId(): int: 1 个
  - definition.method: public function getLabel(): string: 1 个
  - definition.method: public function getName(): string: 1 个
  - definition.method: public function getRoles(): array: 1 个
  - definition.method: public function getStatus(): UserStatus: 1 个
  - definition.method: public function getUpdatedAt(): DateTime: 1 个
  - definition.method: public function hasRole(string $role): bool: 1 个
  - definition.method: public function info(string $message, array $context = []): void {: 1 个
  - definition.method: public function info(string $message, array $context = []): void;: 1 个
  - definition.method: public function isActive(): bool: 1 个
  - definition.method: public function isValid(): bool: 1 个
  - definition.method: public function jsonSerialize(): array: 1 个
  - definition.method: public function removeRole(string $role): self: 1 个
  - definition.method: public function save(User $user): bool: 1 个
  - definition.method: public function save(User $user): bool;: 1 个
  - definition.method: public function setAvatar(?string $avatar): self: 1 个
  - definition.method: public function setEmail(string $email): self: 1 个
  - definition.method: public function setId(int $id): void: 1 个
  - definition.method: public function setName(string $name): self: 1 个
  - definition.method: public function setStatus(UserStatus $status): self: 1 个
  - definition.method: public function suspend(): self: 1 个
  - definition.method: public function toArray(): array: 1 个
  - definition.method: public function touch(): void: 1 个
  - definition.method: public function validate(): array: 1 个

代码块信息 (10 个):
  块 1: 第 31-81 行 (51 行)
     31:     public function getLabel(): string
     32:     {
     33:         return match($this) {
    ... (还有 48 行)

  块 2: 第 83-118 行 (36 行)
     83:     public function isValid(): bool
     84:     {
     85:         return empty($this->validate());
    ... (还有 33 行)

  块 3: 第 121-282 行 (162 行)
    121: class User extends BaseModel
    122: {
    123:     use Validatable;
    ... (还有 159 行)

  块 4: 第 121-221 行 (101 行)
    121: class User extends BaseModel
    122: {
    123:     use Validatable;
    ... (还有 100 行)

  块 5: 第 221-282 行 (62 行)
    221: class User extends BaseModel
    222: ...
    223:     // Status checks
    ... (还有 61 行)

  块 6: 第 285-430 行 (146 行)
    285: class UserRepository implements UserRepositoryInterface
    286: {
    287:     private PDO $pdo;
    ... (还有 143 行)

  块 7: 第 285-385 行 (101 行)
    285: class UserRepository implements UserRepositoryInterface
    286: {
    287:     private PDO $pdo;
    ... (还有 100 行)

  块 8: 第 385-430 行 (46 行)
    385: class UserRepository implements UserRepositoryInterface
    386: ...
    387:         
    ... (还有 45 行)

  块 9: 第 433-491 行 (59 行)
    433: class UserService
    434: {
    435:     private UserRepositoryInterface $repository;
    ... (还有 56 行)

  块 10: 第 493-523 行 (31 行)
    493: function validateEmail(string $email): bool
    494: {
    495:     return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    ... (还有 28 行)


统计信息:
  覆盖率: 147.0%
  块中总行数: 795
  结构类型数: 57
