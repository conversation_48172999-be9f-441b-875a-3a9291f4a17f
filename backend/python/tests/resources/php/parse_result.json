{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php", "filename": "sample.php", "content_length": 13449, "line_count": 541}, "parsing_results": {"key_structure_lines": {"31": "definition.method: public function getLabel(): string", "43": "definition.interface: interface UserRepositoryInterface", "45": "definition.method: public function findById(int $id): ?User;", "46": "definition.method: public function save(User $user): bool;", "47": "definition.method: public function delete(int $id): bool;", "48": "definition.method: public function findAll(): array;", "51": "definition.interface: interface LoggerInterface", "53": "definition.method: public function info(string $message, array $context = []): void;", "54": "definition.method: public function error(string $message, array $context = []): void;", "63": "definition.method: public function getCreatedAt(): DateTime", "68": "definition.method: public function getUpdatedAt(): DateTime", "73": "definition.method: public function touch(): void", "81": "definition.method: abstract public function validate(): array;", "83": "definition.method: public function isValid(): bool", "90": "definition.class: abstract class BaseModel implements JsonSerializable", "96": "definition.method: public function __construct()", "102": "definition.method: public function getId(): int", "107": "definition.method: public function setId(int $id): void", "112": "definition.method: abstract public function toArray(): array;", "114": "definition.method: public function jsonSerialize(): array", "121": "definition.class: class User extends BaseModel", "132": "definition.method: public function __construct(", "145": "definition.method: public function getName(): string", "150": "definition.method: public function getEmail(): string", "155": "definition.method: public function getStatus(): UserStatus", "160": "definition.method: public function getRoles(): array", "165": "definition.method: public function getAvatar(): ?string", "171": "definition.method: public function setName(string $name): self", "178": "definition.method: public function setEmail(string $email): self", "185": "definition.method: public function setStatus(UserStatus $status): self", "192": "definition.method: public function setAvatar(?string $avatar): self", "200": "definition.method: public function addRole(string $role): self", "209": "definition.method: public function removeRole(string $role): self", "216": "definition.method: public function hasRole(string $role): bool", "222": "definition.method: public function isActive(): bool", "227": "definition.method: public function activate(): self", "232": "definition.method: public function suspend(): self", "238": "definition.method: public function validate(): array", "256": "definition.method: public function toArray(): array", "271": "definition.method: public function __toString(): string", "276": "definition.method: public function __clone()", "285": "definition.class: class UserRepository implements UserRepositoryInterface", "290": "definition.method: public function __construct(PDO $pdo, LoggerInterface $logger)", "296": "definition.method: public function findById(int $id): ?User", "317": "definition.method: public function save(User $user): bool", "334": "definition.method: public function delete(int $id): bool", "351": "definition.method: public function findAll(): array", "370": "definition.method: private function insert(User $user): bool", "393": "definition.method: private function update(User $user): bool", "411": "definition.method: private function hydrate(array $data): User", "433": "definition.class: class UserService", "438": "definition.method: public function __construct(", "446": "definition.method: public function createUser(string $name, string $email): ?User", "465": "definition.method: public function activateUser(int $id): bool", "480": "definition.function: function generateRandomString(int $length = 10): string", "493": "definition.function: function validateEmail(string $email): bool", "517": "definition.method: public function info(string $message, array $context = []): void {", "521": "definition.method: public function error(string $message, array $context = []): void {"}, "key_structure_count": 58, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php", "start_line": 31, "end_line": 81, "content": "    public function getLabel(): string\n    {\n        return match($this) {\n            self::ACTIVE => 'Active User',\n            self::INACTIVE => 'Inactive User',\n            self::SUSPENDED => 'Suspended User',\n            self::PENDING => 'Pending Verification',\n        };\n    }\n}\n\n// Interface definitions\ninterface UserRepositoryInterface\n{\n    public function findById(int $id): ?User;\n    public function save(User $user): bool;\n    public function delete(int $id): bool;\n    public function findAll(): array;\n}\n\ninterface LoggerInterface\n{\n    public function info(string $message, array $context = []): void;\n    public function error(string $message, array $context = []): void;\n}\n\n// Trait definitions\ntrait Timestampable\n{\n    protected DateTime $createdAt;\n    protected DateTime $updatedAt;\n    \n    public function getCreatedAt(): DateTime\n    {\n        return $this->createdAt;\n    }\n    \n    public function getUpdatedAt(): DateTime\n    {\n        return $this->updatedAt;\n    }\n    \n    public function touch(): void\n    {\n        $this->updatedAt = new DateTime();\n    }\n}\n\ntrait Validatable\n{\n    abstract public function validate(): array;", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php", "start_line": 83, "end_line": 118, "content": "    public function isValid(): bool\n    {\n        return empty($this->validate());\n    }\n}\n\n// Abstract class\nabstract class BaseModel implements JsonSerializable\n{\n    use Timestampable;\n    \n    protected int $id;\n    \n    public function __construct()\n    {\n        $this->createdAt = new DateTime();\n        $this->updatedAt = new DateTime();\n    }\n    \n    public function getId(): int\n    {\n        return $this->id;\n    }\n    \n    public function setId(int $id): void\n    {\n        $this->id = $id;\n    }\n    \n    abstract public function toArray(): array;\n    \n    public function jsonSerialize(): array\n    {\n        return $this->toArray();\n    }\n}", "line_count": 36}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php", "start_line": 121, "end_line": 282, "content": "class User extends BaseModel\n{\n    use Validatable;\n    \n    private string $name;\n    private string $email;\n    private UserStatus $status;\n    private array $roles = [];\n    private ?string $avatar = null;\n    \n    // Constructor with property promotion (PHP 8.0+)\n    public function __construct(\n        string $name,\n        string $email,\n        UserStatus $status = UserStatus::PENDING\n    ) {\n        parent::__construct();\n        $this->name = $name;\n        $this->email = $email;\n        $this->status = $status;\n        $this->roles = [DEFAULT_ROLE];\n    }\n    \n    // Getters\n    public function getName(): string\n    {\n        return $this->name;\n    }\n    \n    public function getEmail(): string\n    {\n        return $this->email;\n    }\n    \n    public function getStatus(): UserStatus\n    {\n        return $this->status;\n    }\n    \n    public function getRoles(): array\n    {\n        return $this->roles;\n    }\n    \n    public function getAvatar(): ?string\n    {\n        return $this->avatar;\n    }\n    \n    // Setters\n    public function setName(string $name): self\n    {\n        $this->name = $name;\n        $this->touch();\n        return $this;\n    }\n    \n    public function setEmail(string $email): self\n    {\n        $this->email = $email;\n        $this->touch();\n        return $this;\n    }\n    \n    public function setStatus(UserStatus $status): self\n    {\n        $this->status = $status;\n        $this->touch();\n        return $this;\n    }\n    \n    public function setAvatar(?string $avatar): self\n    {\n        $this->avatar = $avatar;\n        $this->touch();\n        return $this;\n    }\n    \n    // Role management\n    public function addRole(string $role): self\n    {\n        if (!in_array($role, $this->roles)) {\n            $this->roles[] = $role;\n            $this->touch();\n        }\n        return $this;\n    }\n    \n    public function removeRole(string $role): self\n    {\n        $this->roles = array_filter($this->roles, fn($r) => $r !== $role);\n        $this->touch();\n        return $this;\n    }\n    \n    public function hasRole(string $role): bool\n    {\n        return in_array($role, $this->roles);\n    }\n    \n    // Status checks\n    public function isActive(): bool\n    {\n        return $this->status === UserStatus::ACTIVE;\n    }\n    \n    public function activate(): self\n    {\n        return $this->setStatus(UserStatus::ACTIVE);\n    }\n    \n    public function suspend(): self\n    {\n        return $this->setStatus(UserStatus::SUSPENDED);\n    }\n    \n    // Validation\n    public function validate(): array\n    {\n        $errors = [];\n        \n        if (empty($this->name)) {\n            $errors[] = 'Name is required';\n        }\n        \n        if (empty($this->email)) {\n            $errors[] = 'Email is required';\n        } elseif (!filter_var($this->email, FILTER_VALIDATE_EMAIL)) {\n            $errors[] = 'Invalid email format';\n        }\n        \n        return $errors;\n    }\n    \n    // Serialization\n    public function toArray(): array\n    {\n        return [\n            'id' => $this->id,\n            'name' => $this->name,\n            'email' => $this->email,\n            'status' => $this->status->value,\n            'roles' => $this->roles,\n            'avatar' => $this->avatar,\n            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),\n            'updated_at' => $this->updatedAt->format('Y-m-d H:i:s'),\n        ];\n    }\n    \n    // Magic methods\n    public function __toString(): string\n    {\n        return sprintf('%s <%s>', $this->name, $this->email);\n    }\n    \n    public function __clone()\n    {\n        $this->id = 0;\n        $this->createdAt = new DateTime();\n        $this->updatedAt = new DateTime();\n    }\n}", "line_count": 162}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php", "start_line": 121, "end_line": 221, "content": "class User extends BaseModel\n{\n    use Validatable;\n    \n    private string $name;\n    private string $email;\n    private UserStatus $status;\n    private array $roles = [];\n    private ?string $avatar = null;\n    \n    // Constructor with property promotion (PHP 8.0+)\n    public function __construct(\n        string $name,\n        string $email,\n        UserStatus $status = UserStatus::PENDING\n    ) {\n        parent::__construct();\n        $this->name = $name;\n        $this->email = $email;\n        $this->status = $status;\n        $this->roles = [DEFAULT_ROLE];\n    }\n    \n    // Getters\n    public function getName(): string\n    {\n        return $this->name;\n    }\n    \n    public function getEmail(): string\n    {\n        return $this->email;\n    }\n    \n    public function getStatus(): UserStatus\n    {\n        return $this->status;\n    }\n    \n    public function getRoles(): array\n    {\n        return $this->roles;\n    }\n    \n    public function getAvatar(): ?string\n    {\n        return $this->avatar;\n    }\n    \n    // Setters\n    public function setName(string $name): self\n    {\n        $this->name = $name;\n        $this->touch();\n        return $this;\n    }\n    \n    public function setEmail(string $email): self\n    {\n        $this->email = $email;\n        $this->touch();\n        return $this;\n    }\n    \n    public function setStatus(UserStatus $status): self\n    {\n        $this->status = $status;\n        $this->touch();\n        return $this;\n    }\n    \n    public function setAvatar(?string $avatar): self\n    {\n        $this->avatar = $avatar;\n        $this->touch();\n        return $this;\n    }\n    \n    // Role management\n    public function addRole(string $role): self\n    {\n        if (!in_array($role, $this->roles)) {\n            $this->roles[] = $role;\n            $this->touch();\n        }\n        return $this;\n    }\n    \n    public function removeRole(string $role): self\n    {\n        $this->roles = array_filter($this->roles, fn($r) => $r !== $role);\n        $this->touch();\n        return $this;\n    }\n    \n    public function hasRole(string $role): bool\n    {\n        return in_array($role, $this->roles);\n    }\n    \n    // Status checks\n...\n}", "line_count": 101}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php", "start_line": 221, "end_line": 282, "content": "class User extends BaseModel\n...\n    // Status checks\n    public function isActive(): bool\n    {\n        return $this->status === UserStatus::ACTIVE;\n    }\n    \n    public function activate(): self\n    {\n        return $this->setStatus(UserStatus::ACTIVE);\n    }\n    \n    public function suspend(): self\n    {\n        return $this->setStatus(UserStatus::SUSPENDED);\n    }\n    \n    // Validation\n    public function validate(): array\n    {\n        $errors = [];\n        \n        if (empty($this->name)) {\n            $errors[] = 'Name is required';\n        }\n        \n        if (empty($this->email)) {\n            $errors[] = 'Email is required';\n        } elseif (!filter_var($this->email, FILTER_VALIDATE_EMAIL)) {\n            $errors[] = 'Invalid email format';\n        }\n        \n        return $errors;\n    }\n    \n    // Serialization\n    public function toArray(): array\n    {\n        return [\n            'id' => $this->id,\n            'name' => $this->name,\n            'email' => $this->email,\n            'status' => $this->status->value,\n            'roles' => $this->roles,\n            'avatar' => $this->avatar,\n            'created_at' => $this->createdAt->format('Y-m-d H:i:s'),\n            'updated_at' => $this->updatedAt->format('Y-m-d H:i:s'),\n        ];\n    }\n    \n    // Magic methods\n    public function __toString(): string\n    {\n        return sprintf('%s <%s>', $this->name, $this->email);\n    }\n    \n    public function __clone()\n    {\n        $this->id = 0;\n        $this->createdAt = new DateTime();\n        $this->updatedAt = new DateTime();\n    }\n}", "line_count": 62}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php", "start_line": 285, "end_line": 430, "content": "class UserRepository implements UserRepositoryInterface\n{\n    private PDO $pdo;\n    private LoggerInterface $logger;\n    \n    public function __construct(PDO $pdo, LoggerInterface $logger)\n    {\n        $this->pdo = $pdo;\n        $this->logger = $logger;\n    }\n    \n    public function findById(int $id): ?User\n    {\n        try {\n            $stmt = $this->pdo->prepare('SELECT * FROM users WHERE id = :id');\n            $stmt->execute(['id' => $id]);\n            $data = $stmt->fetch(PDO::FETCH_ASSOC);\n            \n            if (!$data) {\n                return null;\n            }\n            \n            return $this->hydrate($data);\n        } catch (PDOException $e) {\n            $this->logger->error('Failed to find user by ID', [\n                'id' => $id,\n                'error' => $e->getMessage()\n            ]);\n            return null;\n        }\n    }\n    \n    public function save(User $user): bool\n    {\n        try {\n            if ($user->getId()) {\n                return $this->update($user);\n            } else {\n                return $this->insert($user);\n            }\n        } catch (PDOException $e) {\n            $this->logger->error('Failed to save user', [\n                'user' => $user->toArray(),\n                'error' => $e->getMessage()\n            ]);\n            return false;\n        }\n    }\n    \n    public function delete(int $id): bool\n    {\n        try {\n            $stmt = $this->pdo->prepare('DELETE FROM users WHERE id = :id');\n            $result = $stmt->execute(['id' => $id]);\n            \n            $this->logger->info('User deleted', ['id' => $id]);\n            return $result;\n        } catch (PDOException $e) {\n            $this->logger->error('Failed to delete user', [\n                'id' => $id,\n                'error' => $e->getMessage()\n            ]);\n            return false;\n        }\n    }\n    \n    public function findAll(): array\n    {\n        try {\n            $stmt = $this->pdo->query('SELECT * FROM users ORDER BY created_at DESC');\n            $users = [];\n            \n            while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {\n                $users[] = $this->hydrate($data);\n            }\n            \n            return $users;\n        } catch (PDOException $e) {\n            $this->logger->error('Failed to find all users', [\n                'error' => $e->getMessage()\n            ]);\n            return [];\n        }\n    }\n    \n    private function insert(User $user): bool\n    {\n        $sql = 'INSERT INTO users (name, email, status, roles, avatar, created_at, updated_at) \n                VALUES (:name, :email, :status, :roles, :avatar, :created_at, :updated_at)';\n        \n        $stmt = $this->pdo->prepare($sql);\n        $result = $stmt->execute([\n            'name' => $user->getName(),\n            'email' => $user->getEmail(),\n            'status' => $user->getStatus()->value,\n            'roles' => json_encode($user->getRoles()),\n            'avatar' => $user->getAvatar(),\n            'created_at' => $user->getCreatedAt()->format('Y-m-d H:i:s'),\n            'updated_at' => $user->getUpdatedAt()->format('Y-m-d H:i:s'),\n        ]);\n        \n        if ($result) {\n            $user->setId((int) $this->pdo->lastInsertId());\n        }\n        \n        return $result;\n    }\n    \n    private function update(User $user): bool\n    {\n        $sql = 'UPDATE users SET name = :name, email = :email, status = :status, \n                roles = :roles, avatar = :avatar, updated_at = :updated_at \n                WHERE id = :id';\n        \n        $stmt = $this->pdo->prepare($sql);\n        return $stmt->execute([\n            'id' => $user->getId(),\n            'name' => $user->getName(),\n            'email' => $user->getEmail(),\n            'status' => $user->getStatus()->value,\n            'roles' => json_encode($user->getRoles()),\n            'avatar' => $user->getAvatar(),\n            'updated_at' => $user->getUpdatedAt()->format('Y-m-d H:i:s'),\n        ]);\n    }\n    \n    private function hydrate(array $data): User\n    {\n        $user = new User(\n            $data['name'],\n            $data['email'],\n            UserStatus::from($data['status'])\n        );\n        \n        $user->setId((int) $data['id']);\n        $user->setAvatar($data['avatar']);\n        \n        // Set roles\n        $roles = json_decode($data['roles'], true) ?: [];\n        foreach ($roles as $role) {\n            $user->addRole($role);\n        }\n        \n        return $user;\n    }\n}", "line_count": 146}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php", "start_line": 285, "end_line": 385, "content": "class UserRepository implements UserRepositoryInterface\n{\n    private PDO $pdo;\n    private LoggerInterface $logger;\n    \n    public function __construct(PDO $pdo, LoggerInterface $logger)\n    {\n        $this->pdo = $pdo;\n        $this->logger = $logger;\n    }\n    \n    public function findById(int $id): ?User\n    {\n        try {\n            $stmt = $this->pdo->prepare('SELECT * FROM users WHERE id = :id');\n            $stmt->execute(['id' => $id]);\n            $data = $stmt->fetch(PDO::FETCH_ASSOC);\n            \n            if (!$data) {\n                return null;\n            }\n            \n            return $this->hydrate($data);\n        } catch (PDOException $e) {\n            $this->logger->error('Failed to find user by ID', [\n                'id' => $id,\n                'error' => $e->getMessage()\n            ]);\n            return null;\n        }\n    }\n    \n    public function save(User $user): bool\n    {\n        try {\n            if ($user->getId()) {\n                return $this->update($user);\n            } else {\n                return $this->insert($user);\n            }\n        } catch (PDOException $e) {\n            $this->logger->error('Failed to save user', [\n                'user' => $user->toArray(),\n                'error' => $e->getMessage()\n            ]);\n            return false;\n        }\n    }\n    \n    public function delete(int $id): bool\n    {\n        try {\n            $stmt = $this->pdo->prepare('DELETE FROM users WHERE id = :id');\n            $result = $stmt->execute(['id' => $id]);\n            \n            $this->logger->info('User deleted', ['id' => $id]);\n            return $result;\n        } catch (PDOException $e) {\n            $this->logger->error('Failed to delete user', [\n                'id' => $id,\n                'error' => $e->getMessage()\n            ]);\n            return false;\n        }\n    }\n    \n    public function findAll(): array\n    {\n        try {\n            $stmt = $this->pdo->query('SELECT * FROM users ORDER BY created_at DESC');\n            $users = [];\n            \n            while ($data = $stmt->fetch(PDO::FETCH_ASSOC)) {\n                $users[] = $this->hydrate($data);\n            }\n            \n            return $users;\n        } catch (PDOException $e) {\n            $this->logger->error('Failed to find all users', [\n                'error' => $e->getMessage()\n            ]);\n            return [];\n        }\n    }\n    \n    private function insert(User $user): bool\n    {\n        $sql = 'INSERT INTO users (name, email, status, roles, avatar, created_at, updated_at) \n                VALUES (:name, :email, :status, :roles, :avatar, :created_at, :updated_at)';\n        \n        $stmt = $this->pdo->prepare($sql);\n        $result = $stmt->execute([\n            'name' => $user->getName(),\n            'email' => $user->getEmail(),\n            'status' => $user->getStatus()->value,\n            'roles' => json_encode($user->getRoles()),\n            'avatar' => $user->getAvatar(),\n            'created_at' => $user->getCreatedAt()->format('Y-m-d H:i:s'),\n            'updated_at' => $user->getUpdatedAt()->format('Y-m-d H:i:s'),\n        ]);\n        \n...\n}", "line_count": 101}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php", "start_line": 385, "end_line": 430, "content": "class UserRepository implements UserRepositoryInterface\n...\n        \n        if ($result) {\n            $user->setId((int) $this->pdo->lastInsertId());\n        }\n        \n        return $result;\n    }\n    \n    private function update(User $user): bool\n    {\n        $sql = 'UPDATE users SET name = :name, email = :email, status = :status, \n                roles = :roles, avatar = :avatar, updated_at = :updated_at \n                WHERE id = :id';\n        \n        $stmt = $this->pdo->prepare($sql);\n        return $stmt->execute([\n            'id' => $user->getId(),\n            'name' => $user->getName(),\n            'email' => $user->getEmail(),\n            'status' => $user->getStatus()->value,\n            'roles' => json_encode($user->getRoles()),\n            'avatar' => $user->getAvatar(),\n            'updated_at' => $user->getUpdatedAt()->format('Y-m-d H:i:s'),\n        ]);\n    }\n    \n    private function hydrate(array $data): User\n    {\n        $user = new User(\n            $data['name'],\n            $data['email'],\n            UserStatus::from($data['status'])\n        );\n        \n        $user->setId((int) $data['id']);\n        $user->setAvatar($data['avatar']);\n        \n        // Set roles\n        $roles = json_decode($data['roles'], true) ?: [];\n        foreach ($roles as $role) {\n            $user->addRole($role);\n        }\n        \n        return $user;\n    }\n}", "line_count": 46}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php", "start_line": 433, "end_line": 491, "content": "class UserService\n{\n    private UserRepositoryInterface $repository;\n    private LoggerInterface $logger;\n    \n    public function __construct(\n        UserRepositoryInterface $repository,\n        LoggerInterface $logger\n    ) {\n        $this->repository = $repository;\n        $this->logger = $logger;\n    }\n    \n    public function createUser(string $name, string $email): ?User\n    {\n        $user = new User($name, $email);\n        \n        if (!$user->isValid()) {\n            $this->logger->error('Invalid user data', [\n                'errors' => $user->validate()\n            ]);\n            return null;\n        }\n        \n        if ($this->repository->save($user)) {\n            $this->logger->info('User created', ['user_id' => $user->getId()]);\n            return $user;\n        }\n        \n        return null;\n    }\n    \n    public function activateUser(int $id): bool\n    {\n        $user = $this->repository->findById($id);\n        \n        if (!$user) {\n            $this->logger->error('User not found for activation', ['id' => $id]);\n            return false;\n        }\n        \n        $user->activate();\n        return $this->repository->save($user);\n    }\n}\n\n// Functions\nfunction generateRandomString(int $length = 10): string\n{\n    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\n    $charactersLength = strlen($characters);\n    $randomString = '';\n    \n    for ($i = 0; $i < $length; $i++) {\n        $randomString .= $characters[rand(0, $charactersLength - 1)];\n    }\n    \n    return $randomString;\n}", "line_count": 59}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/php/sample.php", "start_line": 493, "end_line": 523, "content": "function validateEmail(string $email): bool\n{\n    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;\n}\n\n// Arrow functions (PHP 7.4+)\n$multiply = fn($a, $b) => $a * $b;\n$isEven = fn($n) => $n % 2 === 0;\n\n// Anonymous functions\n$greet = function(string $name): string {\n    return \"Hello, {$name}!\";\n};\n\n// Closure with use\n$prefix = 'Mr. ';\n$formatName = function(string $name) use ($prefix): string {\n    return $prefix . $name;\n};\n\n// Example usage\ntry {\n    $pdo = new PDO('sqlite::memory:');\n    $logger = new class implements LoggerInterface {\n        public function info(string $message, array $context = []): void {\n            echo \"[INFO] {$message}\\n\";\n        }\n        \n        public function error(string $message, array $context = []): void {\n            echo \"[ERROR] {$message}\\n\";\n        }", "line_count": 31}], "chunk_count": 10}, "analysis": {"detected_structures": ["definition.method: public function info(string $message, array $context = []): void;", "definition.method: public function error(string $message, array $context = []): void;", "definition.method: public function findAll(): array", "definition.method: public function hasRole(string $role): bool", "definition.class: class UserRepository implements UserRepositoryInterface", "definition.method: public function activate(): self", "definition.method: public function isValid(): bool", "definition.method: public function __construct(PDO $pdo, LoggerInterface $logger)", "definition.method: public function __construct(", "definition.method: public function createUser(string $name, string $email): ?User", "definition.method: private function insert(User $user): bool", "definition.method: public function error(string $message, array $context = []): void {", "definition.method: public function save(User $user): bool;", "definition.method: public function setName(string $name): self", "definition.method: public function getLabel(): string", "definition.method: public function getStatus(): UserStatus", "definition.interface: interface UserRepositoryInterface", "definition.function: function validateEmail(string $email): bool", "definition.class: class User extends BaseModel", "definition.method: public function toArray(): array", "definition.function: function generateRandomString(int $length = 10): string", "definition.method: public function getAvatar(): ?string", "definition.method: public function getId(): int", "definition.method: public function info(string $message, array $context = []): void {", "definition.method: public function jsonSerialize(): array", "definition.method: public function validate(): array", "definition.method: public function findAll(): array;", "definition.method: abstract public function toArray(): array;", "definition.class: class UserService", "definition.method: public function delete(int $id): bool;", "definition.method: public function __construct()", "definition.method: public function touch(): void", "definition.method: private function hydrate(array $data): User", "definition.method: abstract public function validate(): array;", "definition.class: abstract class BaseModel implements JsonSerializable", "definition.method: public function setEmail(string $email): self", "definition.method: public function setStatus(UserStatus $status): self", "definition.method: public function addRole(string $role): self", "definition.method: public function getRoles(): array", "definition.method: public function activateUser(int $id): bool", "definition.method: public function setId(int $id): void", "definition.method: public function suspend(): self", "definition.method: public function save(User $user): bool", "definition.method: public function setAvatar(?string $avatar): self", "definition.method: private function update(User $user): bool", "definition.method: public function getCreatedAt(): DateTime", "definition.method: public function delete(int $id): bool", "definition.method: public function getEmail(): string", "definition.method: public function __clone()", "definition.method: public function __toString(): string", "definition.method: public function getUpdatedAt(): DateTime", "definition.method: public function getName(): string", "definition.method: public function removeRole(string $role): self", "definition.method: public function findById(int $id): ?User;", "definition.method: public function findById(int $id): ?User", "definition.method: public function isActive(): bool", "definition.interface: interface LoggerInterface"], "structure_types_count": 57, "total_lines_in_chunks": 795, "coverage_percentage": 146.95009242144178}}