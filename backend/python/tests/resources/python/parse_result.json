{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/python/sample.py", "filename": "sample.py", "content_length": 3946, "line_count": 169}, "parsing_results": {"key_structure_lines": {"17": "definition.class: @dataclass", "18": "definition.class: class Person:", "24": "definition.function: def __post_init__(self):", "29": "definition.class: class Animal(ABC):", "32": "definition.function: def __init__(self, name: str, species: str):", "36": "definition.function: @abstractmethod", "37": "definition.function: def make_sound(self) -> str:", "41": "definition.function: @property", "42": "definition.function: def description(self) -> str:", "46": "definition.class: class Dog(Animal):", "49": "definition.function: def __init__(self, name: str, breed: str):", "53": "definition.function: def make_sound(self) -> str:", "56": "definition.function: @staticmethod", "57": "definition.function: def is_good_boy() -> bool:", "60": "definition.function: @classmethod", "61": "definition.function: def create_puppy(cls, name: str, breed: str) -> 'Dog':", "65": "definition.function: def simple_function(x: int, y: int = 10) -> int:", "70": "definition.function: async def async_function(data: List[str]) -> Dict[str, int]:", "78": "definition.function: def generator_function(n: int):", "84": "definition.function: @property", "85": "definition.function: def decorated_function():", "104": "definition.function: def function_with_context_managers():", "113": "definition.function: def function_with_exception_handling():", "127": "definition.function: def function_with_global_nonlocal():", "131": "definition.function: def inner_function():", "141": "definition.function: def match_case_example(value):", "155": "definition.function: def typed_function("}, "key_structure_count": 27, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/python/sample.py", "start_line": 6, "end_line": 62, "content": "import os\nimport sys\nfrom typing import List, Dict, Optional\nfrom dataclasses import dataclass\nfrom abc import ABC, abstractmethod\n\n\n# Global variable with type annotation\nglobal_var: int = 42\n\n\n@dataclass\nclass Person:\n    \"\"\"A simple dataclass representing a person.\"\"\"\n    name: str\n    age: int\n    email: Optional[str] = None\n    \n    def __post_init__(self):\n        if self.age < 0:\n            raise ValueError(\"Age cannot be negative\")\n\n\nclass Animal(ABC):\n    \"\"\"Abstract base class for animals.\"\"\"\n    \n    def __init__(self, name: str, species: str):\n        self.name = name\n        self.species = species\n    \n    @abstractmethod\n    def make_sound(self) -> str:\n        \"\"\"Abstract method that must be implemented by subclasses.\"\"\"\n        pass\n    \n    @property\n    def description(self) -> str:\n        return f\"{self.name} is a {self.species}\"\n\n\nclass Dog(Animal):\n    \"\"\"Concrete implementation of Animal for dogs.\"\"\"\n    \n    def __init__(self, name: str, breed: str):\n        super().__init__(name, \"dog\")\n        self.breed = breed\n    \n    def make_sound(self) -> str:\n        return \"Woof!\"\n    \n    @staticmethod\n    def is_good_boy() -> bool:\n        return True\n    \n    @classmethod\n    def create_puppy(cls, name: str, breed: str) -> 'Dog':\n        return cls(name, breed)", "line_count": 57}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/python/sample.py", "start_line": 65, "end_line": 124, "content": "def simple_function(x: int, y: int = 10) -> int:\n    \"\"\"A simple function with type annotations.\"\"\"\n    return x + y\n\n\nasync def async_function(data: List[str]) -> Dict[str, int]:\n    \"\"\"An async function example.\"\"\"\n    result = {}\n    for item in data:\n        result[item] = len(item)\n    return result\n\n\ndef generator_function(n: int):\n    \"\"\"A generator function that yields numbers.\"\"\"\n    for i in range(n):\n        yield i * 2\n\n\n@property\ndef decorated_function():\n    \"\"\"A function with a decorator.\"\"\"\n    return \"decorated\"\n\n\n# Lambda expressions\nsquare = lambda x: x ** 2\nadd_numbers = lambda a, b: a + b\n\n# List comprehension\nnumbers = [x for x in range(10) if x % 2 == 0]\n\n# Dictionary comprehension\nword_lengths = {word: len(word) for word in [\"hello\", \"world\", \"python\"]}\n\n# Set comprehension\nunique_squares = {x**2 for x in range(5)}\n\n\ndef function_with_context_managers():\n    \"\"\"Function demonstrating with statements.\"\"\"\n    with open(\"test.txt\", \"r\") as file:\n        content = file.read()\n    \n    with open(\"output.txt\", \"w\") as output:\n        output.write(content)\n\n\ndef function_with_exception_handling():\n    \"\"\"Function demonstrating try/except statements.\"\"\"\n    try:\n        result = 10 / 0\n    except ZeroDivisionError as e:\n        print(f\"Error: {e}\")\n    except Exception as e:\n        print(f\"Unexpected error: {e}\")\n    else:\n        print(\"No error occurred\")\n    finally:\n        print(\"Cleanup code\")", "line_count": 60}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/python/sample.py", "start_line": 127, "end_line": 161, "content": "def function_with_global_nonlocal():\n    \"\"\"Function demonstrating global and nonlocal statements.\"\"\"\n    global global_var\n    \n    def inner_function():\n        nonlocal local_var\n        local_var = 100\n    \n    local_var = 50\n    inner_function()\n    global_var = 999\n\n\n# Match case statement (Python 3.10+)\ndef match_case_example(value):\n    \"\"\"Function demonstrating match/case statements.\"\"\"\n    match value:\n        case 1:\n            return \"one\"\n        case 2 | 3:\n            return \"two or three\"\n        case x if x > 10:\n            return \"greater than ten\"\n        case _:\n            return \"something else\"\n\n\n# Type annotations\ndef typed_function(\n    param1: str,\n    param2: List[int],\n    param3: Dict[str, Optional[float]]\n) -> Tuple[str, int]:\n    \"\"\"Function with complex type annotations.\"\"\"\n    return param1, len(param2)", "line_count": 35}], "chunk_count": 3}, "analysis": {"detected_structures": ["definition.class: @dataclass", "definition.function: def __init__(self, name: str, species: str):", "definition.function: def function_with_context_managers():", "definition.function: async def async_function(data: List[str]) -> Dict[str, int]:", "definition.function: def create_puppy(cls, name: str, breed: str) -> 'Dog':", "definition.function: def description(self) -> str:", "definition.function: def simple_function(x: int, y: int = 10) -> int:", "definition.class: class Person:", "definition.function: def function_with_global_nonlocal():", "definition.function: @classmethod", "definition.function: def typed_function(", "definition.class: class Dog(Animal):", "definition.function: def function_with_exception_handling():", "definition.function: @abstractmethod", "definition.function: def make_sound(self) -> str:", "definition.function: def decorated_function():", "definition.function: def __post_init__(self):", "definition.function: def match_case_example(value):", "definition.function: @property", "definition.function: def __init__(self, name: str, breed: str):", "definition.function: def inner_function():", "definition.function: def generator_function(n: int):", "definition.function: @staticmethod", "definition.class: class Animal(ABC):", "definition.function: def is_good_boy() -> bool:"], "structure_types_count": 25, "total_lines_in_chunks": 152, "coverage_percentage": 89.94082840236686}}