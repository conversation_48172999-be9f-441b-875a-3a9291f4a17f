FileParser 解析结果报告 - C
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/c/sample.c
  文件名: sample.c
  内容长度: 4685 字符
  行数: 247

关键结构行 (25 个):
  第  16 行: definition.struct: typedef struct {
           内容: typedef struct {
  第  21 行: definition.struct: typedef struct Node {
           内容: typedef struct Node {
  第  26 行: definition.enum: typedef enum {
           内容: typedef enum {
  第  32 行: definition.union: typedef union {
           内容: typedef union {
  第  44 行: definition.function: int add(int a, int b);
           内容: int add(int a, int b);
  第  45 行: definition.function: void print_array(int arr[], int size);
           内容: void print_array(int arr[], int size);
  第  47 行: definition.function: void free_point(Point* p);
           内容: void free_point(Point* p);
  第  50 行: definition.function: int add(int a, int b) {
           内容: int add(int a, int b) {
  第  54 行: definition.function: int subtract(int a, int b) {
           内容: int subtract(int a, int b) {
  第  58 行: definition.function: void print_array(int arr[], int size) {
           内容: void print_array(int arr[], int size) {
  第  66 行: definition.function: Point* create_point(int x, int y) {
           内容: Point* create_point(int x, int y) {
  第  75 行: definition.function: void free_point(Point* p) {
           内容: void free_point(Point* p) {
  第  82 行: definition.function: static int get_next_id(void) {
           内容: static int get_next_id(void) {
  第  88 行: definition.function: inline int max(int a, int b) {
           内容: inline int max(int a, int b) {
  第  93 行: definition.function: int factorial(int n) {
           内容: int factorial(int n) {
  第 103 行: definition.function: int sum_all(int count, ...) {
           内容: int sum_all(int count, ...) {
  第 117 行: definition.function: void swap(int* a, int* b) {
           内容: void swap(int* a, int* b) {
  第 124 行: definition.function_pointer: int (*operation)(int, int) = add;
           内容: int (*operation)(int, int) = add;
  第 127 行: definition.function: void process_matrix(int matrix[][3], int rows) {
           内容: void process_matrix(int matrix[][3], int rows) {
  第 136 行: definition.function: char* string_duplicate(const char* src) {
           内容: char* string_duplicate(const char* src) {
  第 152 行: definition.function: Node* create_node(int data) {
           内容: Node* create_node(int data) {
  第 161 行: definition.function: void insert_node(Node** head, int data) {
           内容: void insert_node(Node** head, int data) {
  第 169 行: definition.function: void print_list(Node* head) {
           内容: void print_list(Node* head) {
  第 178 行: definition.function: void free_list(Node* head) {
           内容: void free_list(Node* head) {
  第 188 行: definition.function: int main(int argc, char* argv[]) {
           内容: int main(int argc, char* argv[]) {

检测到的结构类型:
  - definition.enum: typedef enum {: 1 个
  - definition.function: Node* create_node(int data) {: 1 个
  - definition.function: Point* create_point(int x, int y) {: 1 个
  - definition.function: char* string_duplicate(const char* src) {: 1 个
  - definition.function: inline int max(int a, int b) {: 1 个
  - definition.function: int add(int a, int b) {: 1 个
  - definition.function: int add(int a, int b);: 1 个
  - definition.function: int factorial(int n) {: 1 个
  - definition.function: int main(int argc, char* argv[]) {: 1 个
  - definition.function: int subtract(int a, int b) {: 1 个
  - definition.function: int sum_all(int count, ...) {: 1 个
  - definition.function: static int get_next_id(void) {: 1 个
  - definition.function: void free_list(Node* head) {: 1 个
  - definition.function: void free_point(Point* p) {: 1 个
  - definition.function: void free_point(Point* p);: 1 个
  - definition.function: void insert_node(Node** head, int data) {: 1 个
  - definition.function: void print_array(int arr[], int size) {: 1 个
  - definition.function: void print_array(int arr[], int size);: 1 个
  - definition.function: void print_list(Node* head) {: 1 个
  - definition.function: void process_matrix(int matrix[][3], int rows) {: 1 个
  - definition.function: void swap(int* a, int* b) {: 1 个
  - definition.function_pointer: int (*operation)(int, int) = add;: 1 个
  - definition.struct: typedef struct Node {: 1 个
  - definition.struct: typedef struct {: 1 个
  - definition.union: typedef union {: 1 个

代码块信息 (4 个):
  块 1: 第 11-64 行 (54 行)
     11: #define MAX_SIZE 100
     12: #define PI 3.14159
     13: #define SQUARE(x) ((x) * (x))
    ... (还有 51 行)

  块 2: 第 66-121 行 (56 行)
     66: Point* create_point(int x, int y) {
     67:     Point* p = (Point*)malloc(sizeof(Point));
     68:     if (p != NULL) {
    ... (还有 53 行)

  块 3: 第 124-176 行 (53 行)
    124: int (*operation)(int, int) = add;
    125: 
    126: // Array processing function
    ... (还有 50 行)

  块 4: 第 178-246 行 (69 行)
    178: void free_list(Node* head) {
    179:     Node* current = head;
    180:     while (current != NULL) {
    ... (还有 66 行)


统计信息:
  覆盖率: 93.9%
  块中总行数: 232
  结构类型数: 25
