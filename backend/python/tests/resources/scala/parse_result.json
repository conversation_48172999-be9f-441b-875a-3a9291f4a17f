{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/scala/sample.scala", "filename": "sample.scala", "content_length": 12951, "line_count": 434}, "parsing_results": {"key_structure_lines": {"3": "definition.namespace: package com.example.sample", "12": "definition.class: case class User(", "56": "definition.method: def fromString(value: String): Option[UserStatus] = value.toLowerCase match {", "97": "definition.class: abstract class BaseRepository[T, ID] {", "102": "definition.method: def save(item: T)(implicit ec: ExecutionContext): Future[Result[T]] = {", "113": "definition.class: class InMemoryUserRepository(implicit ec: ExecutionContext) extends UserRepository {", "116": "definition.method: override def findById(id: Long): Future[Option[User]] = Future {", "120": "definition.method: override def save(user: User): Future[Boolean] = Future {", "125": "definition.method: override def delete(id: Long): Future[Boolean] = Future {", "129": "definition.method: override def findAll(): Future[List[User]] = Future {", "133": "definition.method: override def findByStatus(status: UserStatus): Future[List[User]] = Future {", "138": "definition.class: class UserValidator extends Validator[User] {", "139": "definition.method: override def validate(item: User): List[ValidationError] = {", "155": "definition.method: private def isValidEmail(email: String): Boolean = {", "161": "definition.class: class ConsoleLogger extends Logger {", "162": "definition.method: override def info(message: String): Unit = {", "166": "definition.method: override def error(message: String, throwable: Option[Throwable] = None): Unit = {", "171": "definition.method: override def debug(message: String): Unit = {", "176": "definition.class: class UserService(", "184": "definition.method: def createUser(name: String, email: String): Future[Result[User]] = {", "214": "definition.method: def activateUser(id: Long): Future[Boolean] = {", "234": "definition.method: def getUserStats(): Future[Map[UserStatus, Int]] = {", "244": "definition.method: def addEventListener(listener: UserEvent => Unit): Unit = {", "248": "definition.method: private def notifyListeners(event: UserEvent): Unit = {", "257": "definition.method: def createSampleUser(): User = User(", "263": "definition.method: def createUsers(count: Int): List[User] = (1 to count).map { i =>", "279": "definition.class: class ApiClient private(baseUrl: String, timeoutMs: Long) {", "295": "definition.class: implicit class UserOps(user: User) {", "296": "definition.method: def toJson: String = {", "301": "definition.method: def isOlder<PERSON>han(days: Int): Bo<PERSON>an = {", "306": "definition.class: implicit class UserListOps(users: List[User]) {", "316": "definition.method: def measureTime[T](operation: () => T): (T, Long) = {", "323": "definition.method: def retryOperation[T](times: Int = 3, delay: Long = 1000)(operation: () => Future[T])", "325": "definition.method: def attempt(remaining: Int): Future[T] = {", "336": "definition.method: def processInBatches[T, R](items: List[T], batchSize: Int)(processor: List[T] => R): List[R] = {", "343": "definition.method: def processResult[T](result: Result[T]): String = result match {", "350": "definition.method: def processUser(user: User): String = user match {", "357": "definition.method: def extractUserInfo(user: User): (String, String) = user match {"}, "key_structure_count": 38, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/scala/sample.scala", "start_line": 3, "end_line": 63, "content": "package com.example.sample\n\nimport scala.concurrent.{Future, ExecutionContext}\nimport scala.util.{Try, Success, Failure}\nimport scala.collection.mutable\nimport java.time.LocalDateTime\nimport java.util.concurrent.ConcurrentHashMap\n\n// Case classes\ncase class User(\n  id: Long,\n  name: String,\n  email: String,\n  status: UserStatus = UserStatus.Pending,\n  avatar: Option[String] = None,\n  createdAt: LocalDateTime = LocalDateTime.now(),\n  updatedAt: LocalDateTime = LocalDateTime.now()\n) {\n  def isActive: Boolean = status == UserStatus.Active\n  \n  def displayName: String = if (name.trim.isEmpty) \"Unknown User\" else name\n  \n  def activate: User = copy(status = UserStatus.Active, updatedAt = LocalDateTime.now())\n  \n  def updateName(newName: String): User = copy(name = newName, updatedAt = LocalDateTime.now())\n}\n\ncase class UserStats(posts: Int = 0, followers: Int = 0, following: Int = 0)\n\ncase class ValidationError(field: String, message: String)\n\n// Sealed traits and classes\nsealed trait UserStatus {\n  def displayName: String\n}\n\nobject UserStatus {\n  case object Active extends UserStatus {\n    def displayName: String = \"Active\"\n  }\n  \n  case object Inactive extends UserStatus {\n    def displayName: String = \"Inactive\"\n  }\n  \n  case object Suspended extends UserStatus {\n    def displayName: String = \"Suspended\"\n  }\n  \n  case object Pending extends UserStatus {\n    def displayName: String = \"Pending Verification\"\n  }\n  \n  def fromString(value: String): Option[UserStatus] = value.toLowerCase match {\n    case \"active\" => Some(Active)\n    case \"inactive\" => Some(Inactive)\n    case \"suspended\" => Some(Suspended)\n    case \"pending\" => Some(Pending)\n    case _ => None\n  }\n}", "line_count": 61}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/scala/sample.scala", "start_line": 65, "end_line": 136, "content": "sealed trait Result[+T]\ncase class Success[T](data: T) extends Result[T]\ncase class Error(message: String, cause: Option[Throwable] = None) extends Result[Nothing]\ncase object Loading extends Result[Nothing]\n\nsealed trait UserEvent\ncase class UserCreated(user: User) extends UserEvent\ncase class UserUpdated(user: User) extends UserEvent\ncase class UserDeleted(userId: Long) extends UserEvent\ncase class UserStatusChanged(userId: Long, newStatus: UserStatus) extends UserEvent\n\n// Traits\ntrait UserRepository {\n  def findById(id: Long): Future[Option[User]]\n  def save(user: User): Future[Boolean]\n  def delete(id: Long): Future[Boolean]\n  def findAll(): Future[List[User]]\n  def findByStatus(status: UserStatus): Future[List[User]]\n}\n\ntrait Logger {\n  def info(message: String): Unit\n  def error(message: String, throwable: Option[Throwable] = None): Unit\n  def debug(message: String): Unit\n}\n\ntrait Validator[T] {\n  def validate(item: T): List[ValidationError]\n  def isValid(item: T): Boolean = validate(item).isEmpty\n}\n\n// Abstract classes\nabstract class BaseRepository[T, ID] {\n  protected def doSave(item: T): Future[Boolean]\n  protected def doFindById(id: ID): Future[Option[T]]\n  protected def doDelete(id: ID): Future[Boolean]\n  \n  def save(item: T)(implicit ec: ExecutionContext): Future[Result[T]] = {\n    doSave(item).map { success =>\n      if (success) Success(item)\n      else Error(\"Failed to save item\")\n    }.recover {\n      case ex => Error(\"Save operation failed\", Some(ex))\n    }\n  }\n}\n\n// Classes\nclass InMemoryUserRepository(implicit ec: ExecutionContext) extends UserRepository {\n  private val users = new ConcurrentHashMap[Long, User]()\n  \n  override def findById(id: Long): Future[Option[User]] = Future {\n    Option(users.get(id))\n  }\n  \n  override def save(user: User): Future[Boolean] = Future {\n    users.put(user.id, user)\n    true\n  }\n  \n  override def delete(id: Long): Future[Boolean] = Future {\n    users.remove(id) != null\n  }\n  \n  override def findAll(): Future[List[User]] = Future {\n    users.values().asScala.toList\n  }\n  \n  override def findByStatus(status: UserStatus): Future[List[User]] = Future {\n    users.values().asScala.filter(_.status == status).toList\n  }\n}", "line_count": 72}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/scala/sample.scala", "start_line": 138, "end_line": 174, "content": "class UserValidator extends Validator[User] {\n  override def validate(item: User): List[ValidationError] = {\n    val errors = mutable.ListBuffer[ValidationError]()\n    \n    if (item.name.trim.isEmpty) {\n      errors += ValidationError(\"name\", \"Name is required\")\n    }\n    \n    if (item.email.trim.isEmpty) {\n      errors += ValidationError(\"email\", \"Email is required\")\n    } else if (!isValidEmail(item.email)) {\n      errors += ValidationError(\"email\", \"Invalid email format\")\n    }\n    \n    errors.toList\n  }\n  \n  private def isValidEmail(email: String): Boolean = {\n    val emailRegex = \"\"\"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}\"\"\".r\n    emailRegex.matches(email)\n  }\n}\n\nclass ConsoleLogger extends Logger {\n  override def info(message: String): Unit = {\n    println(s\"[${LocalDateTime.now()}] [INFO] $message\")\n  }\n  \n  override def error(message: String, throwable: Option[Throwable] = None): Unit = {\n    println(s\"[${LocalDateTime.now()}] [ERROR] $message\")\n    throwable.foreach(_.printStackTrace())\n  }\n  \n  override def debug(message: String): Unit = {\n    println(s\"[${LocalDateTime.now()}] [DEBUG] $message\")\n  }\n}", "line_count": 37}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/scala/sample.scala", "start_line": 176, "end_line": 270, "content": "class UserService(\n  repository: UserRepository,\n  validator: <PERSON><PERSON><PERSON>[User],\n  logger: Logger\n)(implicit ec: ExecutionContext) {\n  \n  private val eventListeners = mutable.ListBuffer[(UserEvent) => Unit]()\n  \n  def createUser(name: String, email: String): Future[Result[User]] = {\n    val user = User(\n      id = generateUserId(),\n      name = name,\n      email = email\n    )\n    \n    val validationErrors = validator.validate(user)\n    if (validationErrors.nonEmpty) {\n      val errorMessage = validationErrors.map(e => s\"${e.field}: ${e.message}\").mkString(\", \")\n      logger.error(s\"Invalid user data: $errorMessage\")\n      Future.successful(Error(s\"Invalid user data: $errorMessage\"))\n    } else {\n      repository.save(user).map { success =>\n        if (success) {\n          logger.info(s\"User created successfully: ${user.id}\")\n          notifyListeners(UserCreated(user))\n          Success(user)\n        } else {\n          logger.error(\"Failed to save user\")\n          Error(\"Failed to save user\")\n        }\n      }.recover {\n        case ex =>\n          logger.error(\"Error creating user\", Some(ex))\n          Error(s\"Error creating user: ${ex.getMessage}\", Some(ex))\n      }\n    }\n  }\n  \n  def activateUser(id: Long): Future[Boolean] = {\n    repository.findById(id).flatMap {\n      case Some(user) =>\n        val activatedUser = user.activate\n        repository.save(activatedUser).map { success =>\n          if (success) {\n            notifyListeners(UserStatusChanged(id, UserStatus.Active))\n          }\n          success\n        }\n      case None =>\n        logger.error(s\"User not found: $id\")\n        Future.successful(false)\n    }.recover {\n      case ex =>\n        logger.error(\"Error activating user\", Some(ex))\n        false\n    }\n  }\n  \n  def getUserStats(): Future[Map[UserStatus, Int]] = {\n    repository.findAll().map { users =>\n      users.groupBy(_.status).view.mapValues(_.size).toMap\n    }.recover {\n      case ex =>\n        logger.error(\"Error getting user stats\", Some(ex))\n        Map.empty[UserStatus, Int]\n    }\n  }\n  \n  def addEventListener(listener: UserEvent => Unit): Unit = {\n    eventListeners += listener\n  }\n  \n  private def notifyListeners(event: UserEvent): Unit = {\n    eventListeners.foreach(_(event))\n  }\n  \n  private def generateUserId(): Long = System.currentTimeMillis()\n}\n\n// Object definitions\nobject UserFactory {\n  def createSampleUser(): User = User(\n    id = 1L,\n    name = \"John Doe\",\n    email = \"<EMAIL>\"\n  )\n  \n  def createUsers(count: Int): List[User] = (1 to count).map { i =>\n    User(\n      id = i.toLong,\n      name = s\"User $i\",\n      email = s\"user$<EMAIL>\"\n    )\n  }.toList\n}", "line_count": 95}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/scala/sample.scala", "start_line": 272, "end_line": 339, "content": "object Constants {\n  val MaxUsers = 1000\n  val DefaultPageSize = 20\n  val ApiVersion = \"v1\"\n}\n\n// Companion objects\nclass ApiClient private(baseUrl: String, timeoutMs: Long) {\n  // Implementation would go here\n}\n\nobject ApiClient {\n  private val DefaultBaseUrl = \"https://api.example.com\"\n  private val DefaultTimeout = 30000L\n  \n  def apply(): ApiClient = new ApiClient(DefaultBaseUrl, DefaultTimeout)\n  \n  def apply(baseUrl: String): ApiClient = new ApiClient(baseUrl, DefaultTimeout)\n  \n  def apply(baseUrl: String, timeoutMs: Long): ApiClient = new ApiClient(baseUrl, timeoutMs)\n}\n\n// Implicit classes (extension methods)\nimplicit class UserOps(user: User) {\n  def toJson: String = {\n    // Simplified JSON serialization\n    s\"\"\"{\"id\":${user.id},\"name\":\"${user.name}\",\"email\":\"${user.email}\",\"status\":\"${user.status}\"}\"\"\"\n  }\n  \n  def isOlderThan(days: Int): Boolean = {\n    user.createdAt.isBefore(LocalDateTime.now().minusDays(days))\n  }\n}\n\nimplicit class UserListOps(users: List[User]) {\n  def activeUsers: List[User] = users.filter(_.isActive)\n  \n  def byStatus(status: UserStatus): List[User] = users.filter(_.status == status)\n  \n  def sortedByName: List[User] = users.sortBy(_.name)\n}\n\n// Higher-order functions and functional programming\nobject UserUtils {\n  def measureTime[T](operation: () => T): (T, Long) = {\n    val startTime = System.currentTimeMillis()\n    val result = operation()\n    val endTime = System.currentTimeMillis()\n    (result, endTime - startTime)\n  }\n  \n  def retryOperation[T](times: Int = 3, delay: Long = 1000)(operation: () => Future[T])\n                       (implicit ec: ExecutionContext): Future[T] = {\n    def attempt(remaining: Int): Future[T] = {\n      operation().recoverWith {\n        case ex if remaining > 1 =>\n          Thread.sleep(delay)\n          attempt(remaining - 1)\n        case ex => Future.failed(ex)\n      }\n    }\n    attempt(times)\n  }\n  \n  def processInBatches[T, R](items: List[T], batchSize: Int)(processor: List[T] => R): List[R] = {\n    items.grouped(batchSize).map(processor).toList\n  }\n}", "line_count": 68}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/scala/sample.scala", "start_line": 342, "end_line": 433, "content": "object PatternMatchingExamples {\n  def processResult[T](result: Result[T]): String = result match {\n    case Success(data) => s\"Success: $data\"\n    case Error(message, Some(cause)) => s\"Error: $message (caused by: ${cause.getMessage})\"\n    case Error(message, None) => s\"Error: $message\"\n    case Loading => \"Loading...\"\n  }\n  \n  def processUser(user: User): String = user match {\n    case User(_, name, _, UserStatus.Active, _, _, _) => s\"Active user: $name\"\n    case User(_, name, _, UserStatus.Suspended, _, _, _) => s\"Suspended user: $name\"\n    case User(id, _, _, _, _, _, _) if id < 100 => \"Early user\"\n    case _ => \"Regular user\"\n  }\n  \n  def extractUserInfo(user: User): (String, String) = user match {\n    case User(_, name, email, _, _, _, _) => (name, email)\n  }\n}\n\n// Main application\nobject UserApp extends App {\n  implicit val ec: ExecutionContext = ExecutionContext.global\n  \n  val logger = new ConsoleLogger()\n  val repository = new InMemoryUserRepository()\n  val validator = new UserValidator()\n  val service = new UserService(repository, validator, logger)\n  \n  // Add event listener\n  service.addEventListener {\n    case UserCreated(user) => logger.info(s\"User created: ${user.name}\")\n    case UserUpdated(user) => logger.info(s\"User updated: ${user.name}\")\n    case UserDeleted(userId) => logger.info(s\"User deleted: $userId\")\n    case UserStatusChanged(userId, newStatus) => logger.info(s\"User $userId status changed to $newStatus\")\n  }\n  \n  // Example usage\n  val futureResult = for {\n    // Create user\n    userResult <- service.createUser(\"Alice\", \"<EMAIL>\")\n    user = userResult match {\n      case Success(u) => \n        logger.info(s\"Created user: ${u.name}\")\n        Some(u)\n      case Error(message, _) => \n        logger.error(s\"Failed to create user: $message\")\n        None\n      case Loading => None\n    }\n    \n    // Activate user if created successfully\n    activated <- user match {\n      case Some(u) => service.activateUser(u.id)\n      case None => Future.successful(false)\n    }\n    \n    // Get stats\n    stats <- service.getUserStats()\n  } yield {\n    logger.info(s\"User activation result: $activated\")\n    logger.info(s\"User statistics: $stats\")\n  }\n  \n  // Handle the future result\n  futureResult.onComplete {\n    case Success(_) => logger.info(\"Application completed successfully\")\n    case Failure(ex) => logger.error(\"Application failed\", Some(ex))\n  }\n  \n  // Create sample users\n  val sampleUsers = UserFactory.createUsers(5)\n  sampleUsers.foreach { user =>\n    repository.save(user)\n  }\n  \n  // Use extension methods\n  val activeUsers = sampleUsers.activeUsers\n  logger.info(s\"Active users: ${activeUsers.size}\")\n  \n  // Process in batches\n  val batches = UserUtils.processInBatches(sampleUsers, 2) { batch =>\n    logger.info(s\"Processing batch with ${batch.size} users\")\n    batch.map(_.name)\n  }\n  \n  // Pattern matching example\n  sampleUsers.foreach { user =>\n    val info = PatternMatchingExamples.processUser(user)\n    logger.info(info)\n  }\n}", "line_count": 92}], "chunk_count": 6}, "analysis": {"detected_structures": ["definition.method: def addEventListener(listener: UserEvent => Unit): Unit = {", "definition.method: private def isValidEmail(email: String): Boolean = {", "definition.method: def createUser(name: String, email: String): Future[Result[User]] = {", "definition.method: def createUsers(count: Int): List[User] = (1 to count).map { i =>", "definition.method: override def save(user: User): Future[Boolean] = Future {", "definition.method: def fromString(value: String): Option[UserStatus] = value.toLowerCase match {", "definition.method: def processResult[T](result: Result[T]): String = result match {", "definition.method: def activateUser(id: Long): Future[Boolean] = {", "definition.class: abstract class BaseRepository[T, ID] {", "definition.class: case class User(", "definition.class: class UserValidator extends Validator[User] {", "definition.method: override def validate(item: User): List[ValidationError] = {", "definition.method: override def findAll(): Future[List[User]] = Future {", "definition.class: class ConsoleLogger extends Logger {", "definition.class: class UserService(", "definition.method: def createSampleUser(): User = User(", "definition.method: override def error(message: String, throwable: Option[Throwable] = None): Unit = {", "definition.method: private def notifyListeners(event: UserEvent): Unit = {", "definition.method: def processInBatches[T, R](items: List[T], batchSize: Int)(processor: List[T] => R): List[R] = {", "definition.method: override def findByStatus(status: UserStatus): Future[List[User]] = Future {", "definition.method: override def findById(id: Long): Future[Option[User]] = Future {", "definition.class: implicit class UserListOps(users: List[User]) {", "definition.method: def measureTime[T](operation: () => T): (T, Long) = {", "definition.method: def save(item: T)(implicit ec: ExecutionContext): Future[Result[T]] = {", "definition.method: override def debug(message: String): Unit = {", "definition.method: def processUser(user: User): String = user match {", "definition.method: override def info(message: String): Unit = {", "definition.method: override def delete(id: Long): Future[Boolean] = Future {", "definition.method: def retryOperation[T](times: Int = 3, delay: Long = 1000)(operation: () => Future[T])", "definition.method: def toJson: String = {", "definition.class: class InMemoryUserRepository(implicit ec: ExecutionContext) extends UserRepository {", "definition.method: def extractUserInfo(user: User): (String, String) = user match {", "definition.namespace: package com.example.sample", "definition.class: class ApiClient private(baseUrl: String, timeoutMs: Long) {", "definition.method: def getUserStats(): Future[Map[UserStatus, Int]] = {", "definition.method: def attempt(remaining: Int): Future[T] = {", "definition.class: implicit class UserOps(user: User) {", "definition.method: def isOlder<PERSON>han(days: Int): Bo<PERSON>an = {"], "structure_types_count": 38, "total_lines_in_chunks": 425, "coverage_percentage": 97.92626728110599}}