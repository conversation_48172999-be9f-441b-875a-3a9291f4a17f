FileParser 解析结果总结报告
============================================================

生成时间: 2025-08-24T02:01:44.497984
解析器配置: {'min_chunk_size': 50, 'max_chunk_size': 100, 'overflow_size': 5}

成功解析的语言 (15):
  ✅ python      : 27 结构,  3 块,  89.9% 覆盖率
  ✅ java        : 32 结构,  3 块,  98.3% 覆盖率
  ✅ javascript  : 29 结构,  4 块,  89.1% 覆盖率
  ✅ typescript  : 40 结构,  4 块,  93.4% 覆盖率
  ✅ c           : 25 结构,  4 块,  93.9% 覆盖率
  ✅ cpp         : 43 结构,  6 块, 139.4% 覆盖率
  ✅ go          : 14 结构,  5 块,  77.6% 覆盖率
  ✅ rust        : 38 结构,  5 块,  97.4% 覆盖率
  ✅ php         : 58 结构, 10 块, 147.0% 覆盖率
  ✅ swift       : 34 结构,  5 块,  94.1% 覆盖率
  ✅ kotlin      : 65 结构,  5 块,  95.6% 覆盖率
  ✅ scala       : 38 结构,  6 块,  97.9% 覆盖率
  ✅ css         :  0 结构,  7 块,  96.8% 覆盖率
  ✅ jsx         :  0 结构,  0 块,   0.0% 覆盖率
  ✅ tsx         :  7 结构,  9 块, 137.7% 覆盖率

解析失败的语言 (2):
  ❌ ruby        : list index out of range
  ❌ html        : list index out of range

结构类型统计:
  - definition.class: @Serializable: 1 种语言
  - definition.class: @dataclass: 1 种语言
  - definition.class: abstract class BaseModel implements JsonSerializable: 1 种语言
  - definition.class: abstract class BaseRepository<T, ID> {: 1 种语言
  - definition.class: abstract class BaseRepository[T, ID] {: 1 种语言
  - definition.class: abstract class Shape {: 2 种语言
  - definition.class: case class User(: 1 种语言
  - definition.class: class Animal {: 1 种语言
  - definition.class: class Animal(ABC):: 1 种语言
  - definition.class: class ApiClient private(baseUrl: String, timeoutMs: Long) {: 1 种语言
  - definition.class: class ApiClient {: 1 种语言
  - definition.class: class Calculator {: 1 种语言
  - definition.class: class Circle : public Shape {: 1 种语言
  - definition.class: class Circle extends Shape implements Drawable {: 1 种语言
  - definition.class: class Circle extends Shape {: 1 种语言
  - definition.class: class ConsoleLogger : Logger {: 1 种语言
  - definition.class: class ConsoleLogger extends Logger {: 1 种语言
  - definition.class: class Container {: 1 种语言
  - definition.class: class Dog extends Animal {: 1 种语言
  - definition.class: class Dog(Animal):: 1 种语言
  - definition.class: class InMemoryUserRepository : UserRepository {: 1 种语言
  - definition.class: class InMemoryUserRepository(implicit ec: ExecutionContext) extends UserRepository {: 1 种语言
  - definition.class: class Logger {: 1 种语言
  - definition.class: class Person:: 1 种语言
  - definition.class: class Point {: 1 种语言
  - definition.class: class Repository<T: Codable> {: 1 种语言
  - definition.class: class Shape {: 1 种语言
  - definition.class: class User extends BaseModel: 1 种语言
  - definition.class: class UserRepository implements UserRepositoryInterface: 1 种语言
  - definition.class: class UserRepository: UserRepositoryProtocol {: 1 种语言
  - definition.class: class UserService: 1 种语言
  - definition.class: class UserService implements Repository<User> {: 1 种语言
  - definition.class: class UserService {: 1 种语言
  - definition.class: class UserService(: 2 种语言
  - definition.class: class UserValidator : Validator<User> {: 1 种语言
  - definition.class: class UserValidator extends Validator[User] {: 1 种语言
  - definition.class: data class Created(val user: User) : UserEvent(): 1 种语言
  - definition.class: data class Deleted(val userId: Long) : UserEvent(): 1 种语言
  - definition.class: data class Error(val message: String, val cause: Throwable? = null) : Result<Nothing>(): 1 种语言
  - definition.class: data class StatusChanged(val userId: Long, val newStatus: UserStatus) : UserEvent(): 1 种语言
  - definition.class: data class Success<T>(val data: T) : Result<T>(): 1 种语言
  - definition.class: data class Updated(val user: User) : UserEvent(): 1 种语言
  - definition.class: enum Level: String {: 1 种语言
  - definition.class: enum NetworkError: Error {: 1 种语言
  - definition.class: enum UserStatus: String, CaseIterable {: 1 种语言
  - definition.class: enum class LogLevel {: 1 种语言
  - definition.class: enum class UserStatus(val displayName: String) {: 1 种语言
  - definition.class: export class Logger {: 1 种语言
  - definition.class: export default class DefaultExport {: 1 种语言
  - definition.class: implicit class UserListOps(users: List[User]) {: 1 种语言
  - definition.class: implicit class UserOps(user: User) {: 1 种语言
  - definition.class: interface Logger {: 1 种语言
  - definition.class: interface UserRepository {: 1 种语言
  - definition.class: interface Validator<T> {: 1 种语言
  - definition.class: public class InnerClass {: 1 种语言
  - definition.class: public class Sample {: 1 种语言
  - definition.class: public static class StaticNestedClass {: 1 种语言
  - definition.class: sealed class Result<out T> {: 1 种语言
  - definition.class: sealed class UserEvent {: 1 种语言
  - definition.class: struct User: Codable, Validatable {: 1 种语言
  - definition.constructor: Circle(const Point& center, double radius, const std::string& color): 1 种语言
  - definition.constructor: Color(String description) {: 1 种语言
  - definition.constructor: Container() = default;: 1 种语言
  - definition.constructor: Container(std::initializer_list<T> init) : data_(init) {}: 1 种语言
  - definition.constructor: Point() : x_(0.0), y_(0.0) {}: 1 种语言
  - definition.constructor: Point(Point&& other) noexcept : x_(other.x_), y_(other.y_) {: 1 种语言
  - definition.constructor: Point(const Point& other) : x_(other.x_), y_(other.y_) {}: 1 种语言
  - definition.constructor: Point(double x, double y) : x_(x), y_(y) {}: 1 种语言
  - definition.constructor: Shape(const std::string& color) : color_(color) {}: 1 种语言
  - definition.constructor: T max(const T& a, const T& b) {: 1 种语言
  - definition.constructor: auto add(const T& a, const U& b) -> decltype(a + b) {: 1 种语言
  - definition.constructor: auto filter(Predicate pred) const {: 1 种语言
  - definition.constructor: int main() {: 1 种语言
  - definition.constructor: public Circle(String color, double radius) {: 1 种语言
  - definition.constructor: public InnerClass(String value) {: 1 种语言
  - definition.constructor: public Person(String name, int age) {: 1 种语言
  - definition.constructor: public Sample() {: 1 种语言
  - definition.constructor: public Sample(int value, List<String> items) {: 1 种语言
  - definition.constructor: public Shape(String color) {: 1 种语言
  - definition.constructor: void add(U&& item) {: 1 种语言
  - definition.constructor: void modernCppFeatures() {: 1 种语言
  - definition.constructor: void print(Args... args) {: 1 种语言
  - definition.destructor: virtual ~Shape() = default;: 1 种语言
  - definition.destructor: ~Point() = default;: 1 种语言
  - definition.enum: enum Color {: 1 种语言
  - definition.enum: enum DatabaseError {: 1 种语言
  - definition.enum: enum Direction {: 1 种语言
  - definition.enum: enum Status {: 1 种语言
  - definition.enum: enum UserStatus {: 1 种语言
  - definition.enum: typedef enum {: 1 种语言
  - definition.function: @abstractmethod: 1 种语言
  - definition.function: @classmethod: 1 种语言
  - definition.function: @property: 1 种语言
  - definition.function: @staticmethod: 1 种语言
  - definition.function: Node* create_node(int data) {: 1 种语言
  - definition.function: Point* create_point(int x, int y) {: 1 种语言
  - definition.function: async def async_function(data: List[str]) -> Dict[str, int]:: 1 种语言
  - definition.function: async fn process_users_async(&self, user_ids: Vec<UserId>) -> Vec<User> {: 1 种语言
  - definition.function: async function fetchData(url) {: 1 种语言
  - definition.function: char* string_duplicate(const char* src) {: 1 种语言
  - definition.function: const arrowFunction = (x, y) => x * y;: 1 种语言
  - definition.function: const asyncArrowFunction = async (id) => {: 1 种语言
  - definition.function: const functionExpression = function(x) {: 1 种语言
  - definition.function: const higherOrderFunction = (callback) => {: 1 种语言
  - definition.function: const namedFunctionExpression = function square(x) {: 1 种语言
  - definition.function: const noParamArrow = () => 'no params';: 1 种语言
  - definition.function: const singleParamArrow = x => x * 2;: 1 种语言
  - definition.function: def __init__(self, name: str, breed: str):: 1 种语言
  - definition.function: def __init__(self, name: str, species: str):: 1 种语言
  - definition.function: def __post_init__(self):: 1 种语言
  - definition.function: def create_puppy(cls, name: str, breed: str) -> 'Dog':: 1 种语言
  - definition.function: def decorated_function():: 1 种语言
  - definition.function: def description(self) -> str:: 1 种语言
  - definition.function: def function_with_context_managers():: 1 种语言
  - definition.function: def function_with_exception_handling():: 1 种语言
  - definition.function: def function_with_global_nonlocal():: 1 种语言
  - definition.function: def generator_function(n: int):: 1 种语言
  - definition.function: def inner_function():: 1 种语言
  - definition.function: def is_good_boy() -> bool:: 1 种语言
  - definition.function: def make_sound(self) -> str:: 1 种语言
  - definition.function: def match_case_example(value):: 1 种语言
  - definition.function: def simple_function(x: int, y: int = 10) -> int:: 1 种语言
  - definition.function: def typed_function(: 1 种语言
  - definition.function: export function formatDate(date: Date): string {: 1 种语言
  - definition.function: export function isValidEmail(email: string): boolean {: 1 种语言
  - definition.function: fn activate(&mut self) {: 1 种语言
  - definition.function: fn activate_user(&self, id: UserId) -> Result<()> {: 1 种语言
  - definition.function: fn clone(&self) -> Self {: 1 种语言
  - definition.function: fn closure_examples() {: 1 种语言
  - definition.function: fn create_user(&self, name: UserName, email: String) -> Result<UserId> {: 1 种语言
  - definition.function: fn divide(a: f64, b: f64) -> Result<f64> {: 1 种语言
  - definition.function: fn find_by_predicate<T, F>(items: &[T], predicate: F) -> Option<&T>: 1 种语言
  - definition.function: fn find_user_by_email(users: &[User], email: &str) -> Option<&User> {: 1 种语言
  - definition.function: fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {: 1 种语言
  - definition.function: fn generate_user_id() -> UserId {: 1 种语言
  - definition.function: fn generate_verification_code() -> String {: 1 种语言
  - definition.function: fn get(&self, id: &UserId) -> Option<&T> {: 1 种语言
  - definition.function: fn get_user(&self, id: UserId) -> Option<User> {: 1 种语言
  - definition.function: fn insert(&mut self, id: UserId, item: T) -> Result<()> {: 1 种语言
  - definition.function: fn into_name(self) -> UserName {: 1 种语言
  - definition.function: fn is_active(&self) -> bool {: 1 种语言
  - definition.function: fn len(&self) -> usize {: 1 种语言
  - definition.function: fn longest<'a>(x: &'a str, y: &'a str) -> &'a str {: 1 种语言
  - definition.function: fn main() -> Result<()> {: 1 种语言
  - definition.function: fn new() -> Self {: 1 种语言
  - definition.function: fn new(id: UserId, name: UserName, email: String) -> Self {: 1 种语言
  - definition.function: fn new(max_size: usize) -> Self {: 1 种语言
  - definition.function: fn process_user_status(status: &UserStatus) -> String {: 1 种语言
  - definition.function: fn remove(&mut self, id: &UserId) -> Option<T> {: 1 种语言
  - definition.function: fn serialize(&self) -> String {: 1 种语言
  - definition.function: fn test_repository_operations() {: 1 种语言
  - definition.function: fn test_user_activation() {: 1 种语言
  - definition.function: fn test_user_creation() {: 1 种语言
  - definition.function: fn validate(&self) -> Result<()> {: 1 种语言
  - definition.function: fun <T> List<T>.chunked(size: Int): List<List<T>> {: 1 种语言
  - definition.function: fun List<User>.activeUsers(): List<User> = filter { it.isActive }: 1 种语言
  - definition.function: fun List<User>.byStatus(status: UserStatus): List<User> = filter { it.status == status }: 1 种语言
  - definition.function: fun String.toUser(): User? = try {: 1 种语言
  - definition.function: fun User.toJson(): String = Json.encodeToString(User.serializer(), this): 1 种语言
  - definition.function: fun activate() {: 1 种语言
  - definition.function: fun addEventListener(listener: (UserEvent) -> Unit) {: 1 种语言
  - definition.function: fun create(): ApiClient = ApiClient(): 1 种语言
  - definition.function: fun createSampleUser(): User = User(: 1 种语言
  - definition.function: fun createUsers(count: Int): List<User> = (1..count).map { i ->: 1 种语言
  - definition.function: fun createWithTimeout(timeoutMs: Long): ApiClient {: 1 种语言
  - definition.function: fun debug(message: String) = log(LogLevel.DEBUG, message): 1 种语言
  - definition.function: fun error(message: String, throwable: Throwable? = null) = log(LogLevel.ERROR, message, throwable): 1 种语言
  - definition.function: fun fromString(value: String): UserStatus? {: 1 种语言
  - definition.function: fun info(message: String) = log(LogLevel.INFO, message): 1 种语言
  - definition.function: fun isValid(item: T): Boolean = validate(item).isEmpty(): 1 种语言
  - definition.function: fun log(level: LogLevel, message: String, throwable: Throwable? = null): 1 种语言
  - definition.function: fun updateName(newName: String) {: 1 种语言
  - definition.function: fun validate(item: T): List<String>: 1 种语言
  - definition.function: function assertIsNumber(value: any): asserts value is number {: 1 种语言
  - definition.function: function combine(a: StringOrNumber, b: StringOrNumber): StringOrNumber {: 1 种语言
  - definition.function: function combine(a: number, b: number): number;: 1 种语言
  - definition.function: function combine(a: string, b: string): string;: 1 种语言
  - definition.function: function generateRandomString(int $length = 10): string: 1 种语言
  - definition.function: function greetUser(name = 'Guest', greeting = 'Hello') {: 1 种语言
  - definition.function: function isString(value: any): value is string {: 1 种语言
  - definition.function: function isUser(obj: any): obj is User {: 1 种语言
  - definition.function: function logged(target: any, propertyName: string, descriptor: PropertyDescriptor) {: 1 种语言
  - definition.function: function privateFunction() {: 1 种语言
  - definition.function: function processData<T>(: 1 种语言
  - definition.function: function processUser({ name = 'Unknown', age = 0, email = null } = {}) {: 1 种语言
  - definition.function: function regularFunction(param1, param2) {: 1 种语言
  - definition.function: function sumAll(...args) {: 1 种语言
  - definition.function: function validateEmail(string $email): bool: 1 种语言
  - definition.function: function* numberGenerator(max) {: 1 种语言
  - definition.function: inline fun <T> measureTime(operation: () -> T): Pair<T, Long> {: 1 种语言
  - definition.function: inline int max(int a, int b) {: 1 种语言
  - definition.function: int add(int a, int b) {: 1 种语言
  - definition.function: int add(int a, int b);: 1 种语言
  - definition.function: int factorial(int n) {: 1 种语言
  - definition.function: int main(int argc, char* argv[]) {: 1 种语言
  - definition.function: int subtract(int a, int b) {: 1 种语言
  - definition.function: int sum_all(int count, ...) {: 1 种语言
  - definition.function: override fun log(level: LogLevel, message: String, throwable: Throwable?) {: 1 种语言
  - definition.function: override fun validate(item: User): List<String> {: 1 种语言
  - definition.function: override suspend fun delete(id: Long): Boolean = withContext(Dispatchers.IO) {: 1 种语言
  - definition.function: override suspend fun findAll(): List<User> = withContext(Dispatchers.IO) {: 1 种语言
  - definition.function: override suspend fun findById(id: Long): User? = withContext(Dispatchers.IO) {: 1 种语言
  - definition.function: override suspend fun findByStatus(status: UserStatus): List<User> = withContext(Dispatchers.IO) {: 1 种语言
  - definition.function: override suspend fun save(user: User): Boolean = withContext(Dispatchers.IO) {: 1 种语言
  - definition.function: private fun generateUserId(): Long = System.currentTimeMillis(): 1 种语言
  - definition.function: private fun isValidEmail(email: String): Boolean {: 1 种语言
  - definition.function: private fun notifyListeners(event: UserEvent) {: 1 种语言
  - definition.function: protected abstract suspend fun doDelete(id: ID): Boolean: 1 种语言
  - definition.function: protected abstract suspend fun doFindById(id: ID): T?: 1 种语言
  - definition.function: protected abstract suspend fun doSave(item: T): Boolean: 1 种语言
  - definition.function: static int get_next_id(void) {: 1 种语言
  - definition.function: suspend fun <T> retryOperation(: 1 种语言
  - definition.function: suspend fun activateUser(id: Long): Boolean = try {: 1 种语言
  - definition.function: suspend fun createUser(name: String, email: String): Result<User> = try {: 1 种语言
  - definition.function: suspend fun delete(id: Long): Boolean: 1 种语言
  - definition.function: suspend fun findAll(): List<User>: 1 种语言
  - definition.function: suspend fun findById(id: Long): User?: 1 种语言
  - definition.function: suspend fun findByStatus(status: UserStatus): List<User>: 1 种语言
  - definition.function: suspend fun getUserStats(): Map<UserStatus, Int> = try {: 1 种语言
  - definition.function: suspend fun main() {: 1 种语言
  - definition.function: suspend fun save(item: T): Result<T> = try {: 1 种语言
  - definition.function: suspend fun save(user: User): Boolean: 1 种语言
  - definition.function: void free_list(Node* head) {: 1 种语言
  - definition.function: void free_point(Point* p) {: 1 种语言
  - definition.function: void free_point(Point* p);: 1 种语言
  - definition.function: void insert_node(Node** head, int data) {: 1 种语言
  - definition.function: void print_array(int arr[], int size) {: 1 种语言
  - definition.function: void print_array(int arr[], int size);: 1 种语言
  - definition.function: void print_list(Node* head) {: 1 种语言
  - definition.function: void process_matrix(int matrix[][3], int rows) {: 1 种语言
  - definition.function: void swap(int* a, int* b) {: 1 种语言
  - definition.function_pointer: int (*operation)(int, int) = add;: 1 种语言
  - definition.interface: interface ApiResponse<T> {: 1 种语言
  - definition.interface: interface Drawable {: 1 种语言
  - definition.interface: interface FormErrors {: 1 种语言
  - definition.interface: interface LoadingProps {: 1 种语言
  - definition.interface: interface LoggerInterface: 1 种语言
  - definition.interface: interface Repository<T> {: 1 种语言
  - definition.interface: interface User {: 2 种语言
  - definition.interface: interface UserPreferences {: 1 种语言
  - definition.interface: interface UserProfileProps {: 1 种语言
  - definition.interface: interface UserRepositoryInterface: 1 种语言
  - definition.interface: interface UserStats {: 1 种语言
  - definition.interface: interface UserStatsProps {: 1 种语言
  - definition.interface: interface Window {: 1 种语言
  - definition.interface: protocol UserRepositoryProtocol {: 1 种语言
  - definition.interface: protocol Validatable {: 1 种语言
  - definition.method: @Override: 1 种语言
  - definition.method: abstract getArea(): number;: 1 种语言
  - definition.method: abstract getPerimeter(): number;: 1 种语言
  - definition.method: abstract public function toArray(): array;: 1 种语言
  - definition.method: abstract public function validate(): array;: 1 种语言
  - definition.method: add(a: number, b: number): number {: 1 种语言
  - definition.method: async delete(id: number): Promise<void> {: 1 种语言
  - definition.method: async findByField<K extends keyof User>(field: K, value: User[K]): Promise<User[]> {: 1 种语言
  - definition.method: async findById(id: number): Promise<User | null> {: 1 种语言
  - definition.method: async save(user: User): Promise<User> {: 1 种语言
  - definition.method: auto begin() const { return data_.begin(); }: 1 种语言
  - definition.method: auto begin() { return data_.begin(); }: 1 种语言
  - definition.method: auto end() const { return data_.end(); }: 1 种语言
  - definition.method: auto end() { return data_.end(); }: 1 种语言
  - definition.method: constructor(color: string, private radius: number) {: 1 种语言
  - definition.method: constructor(private apiUrl: string) {}: 1 种语言
  - definition.method: def activateUser(id: Long): Future[Boolean] = {: 1 种语言
  - definition.method: def addEventListener(listener: UserEvent => Unit): Unit = {: 1 种语言
  - definition.method: def attempt(remaining: Int): Future[T] = {: 1 种语言
  - definition.method: def createSampleUser(): User = User(: 1 种语言
  - definition.method: def createUser(name: String, email: String): Future[Result[User]] = {: 1 种语言
  - definition.method: def createUsers(count: Int): List[User] = (1 to count).map { i =>: 1 种语言
  - definition.method: def extractUserInfo(user: User): (String, String) = user match {: 1 种语言
  - definition.method: def fromString(value: String): Option[UserStatus] = value.toLowerCase match {: 1 种语言
  - definition.method: def getUserStats(): Future[Map[UserStatus, Int]] = {: 1 种语言
  - definition.method: def isOlderThan(days: Int): Boolean = {: 1 种语言
  - definition.method: def measureTime[T](operation: () => T): (T, Long) = {: 1 种语言
  - definition.method: def processInBatches[T, R](items: List[T], batchSize: Int)(processor: List[T] => R): List[R] = {: 1 种语言
  - definition.method: def processResult[T](result: Result[T]): String = result match {: 1 种语言
  - definition.method: def processUser(user: User): String = user match {: 1 种语言
  - definition.method: def retryOperation[T](times: Int = 3, delay: Long = 1000)(operation: () => Future[T]): 1 种语言
  - definition.method: def save(item: T)(implicit ec: ExecutionContext): Future[Result[T]] = {: 1 种语言
  - definition.method: def toJson: String = {: 1 种语言
  - definition.method: default void print() {: 1 种语言
  - definition.method: delete(id: number): Promise<void>;: 1 种语言
  - definition.method: double area() const override {: 1 种语言
  - definition.method: double getRadius() const { return radius_; }: 1 种语言
  - definition.method: double getX() const { return x_; }: 1 种语言
  - definition.method: double getY() const { return y_; }: 1 种语言
  - definition.method: double perimeter() const override {: 1 种语言
  - definition.method: findById(id: number): Promise<T | null>;: 1 种语言
  - definition.method: func activateUser(id: Int) -> Bool {: 1 种语言
  - definition.method: func all() -> [T] {: 1 种语言
  - definition.method: func createUser(name: String, email: String) -> Result<User, NetworkError> {: 1 种语言
  - definition.method: func delete(by key: String) -> Bool {: 1 种语言
  - definition.method: func deleteUser(by id: Int) -> Bool {: 1 种语言
  - definition.method: func error(_ message: String) {: 1 种语言
  - definition.method: func exampleUsage() {: 1 种语言
  - definition.method: func find(by key: String) -> T? {: 1 种语言
  - definition.method: func findUser(by id: Int) -> User? {: 1 种语言
  - definition.method: func getAllUsers() -> [User] {: 1 种语言
  - definition.method: func info(_ message: String) {: 1 种语言
  - definition.method: func processUsers<T: Sequence>(_ users: T, transform: (User) -> Void) where T.Element == User {: 1 种语言
  - definition.method: func save(_ item: T, with key: String) {: 1 种语言
  - definition.method: func saveUser(_ user: User) -> Bool {: 1 种语言
  - definition.method: func toJSON() -> String? {: 1 种语言
  - definition.method: func validate() -> [String] {: 1 种语言
  - definition.method: func validateEmail(_ email: String) -> Bool {: 1 种语言
  - definition.method: func warning(_ message: String) {: 1 种语言
  - definition.method: get counter() {: 1 种语言
  - definition.method: get description() {: 1 种语言
  - definition.method: get displayName() {: 1 种语言
  - definition.method: getArea(): number {: 1 种语言
  - definition.method: getColor(): string {: 1 种语言
  - definition.method: getPerimeter(): number {: 1 种语言
  - definition.method: getPrivateData() {: 1 种语言
  - definition.method: getValue() {: 1 种语言
  - definition.method: greet() {: 1 种语言
  - definition.method: makeSound() {: 1 种语言
  - definition.method: multiply(a: number, b: number): number {: 1 种语言
  - definition.method: mutating func activate() {: 1 种语言
  - definition.method: mutating func updateName(_ newName: String) {: 1 种语言
  - definition.method: override def debug(message: String): Unit = {: 1 种语言
  - definition.method: override def delete(id: Long): Future[Boolean] = Future {: 1 种语言
  - definition.method: override def error(message: String, throwable: Option[Throwable] = None): Unit = {: 1 种语言
  - definition.method: override def findAll(): Future[List[User]] = Future {: 1 种语言
  - definition.method: override def findById(id: Long): Future[Option[User]] = Future {: 1 种语言
  - definition.method: override def findByStatus(status: UserStatus): Future[List[User]] = Future {: 1 种语言
  - definition.method: override def info(message: String): Unit = {: 1 种语言
  - definition.method: override def save(user: User): Future[Boolean] = Future {: 1 种语言
  - definition.method: override def validate(item: User): List[ValidationError] = {: 1 种语言
  - definition.method: private def isValidEmail(email: String): Boolean = {: 1 种语言
  - definition.method: private def notifyListeners(event: UserEvent): Unit = {: 1 种语言
  - definition.method: private func generateUserId() -> Int {: 1 种语言
  - definition.method: private func isValidEmail(_ email: String) -> Bool {: 1 种语言
  - definition.method: private func log(_ message: String, level: Level) {: 1 种语言
  - definition.method: private function hydrate(array $data): User: 1 种语言
  - definition.method: private function insert(User $user): bool: 1 种语言
  - definition.method: private function update(User $user): bool: 1 种语言
  - definition.method: private int privateMethod(int a, int b) {: 1 种语言
  - definition.method: protected constructor(protected color: string) {}: 1 种语言
  - definition.method: protected static String staticMethod(String input) {: 1 种语言
  - definition.method: public <T> List<T> genericMethod(T item, int count) {: 1 种语言
  - definition.method: public String getColor() {: 1 种语言
  - definition.method: public String getDescription() {: 1 种语言
  - definition.method: public String getDisplayName() {: 1 种语言
  - definition.method: public String getInnerField() {: 1 种语言
  - definition.method: public abstract double getArea();: 1 种语言
  - definition.method: public function __clone(): 1 种语言
  - definition.method: public function __construct(: 1 种语言
  - definition.method: public function __construct(): 1 种语言
  - definition.method: public function __construct(PDO $pdo, LoggerInterface $logger): 1 种语言
  - definition.method: public function __toString(): string: 1 种语言
  - definition.method: public function activate(): self: 1 种语言
  - definition.method: public function activateUser(int $id): bool: 1 种语言
  - definition.method: public function addRole(string $role): self: 1 种语言
  - definition.method: public function createUser(string $name, string $email): ?User: 1 种语言
  - definition.method: public function delete(int $id): bool: 1 种语言
  - definition.method: public function delete(int $id): bool;: 1 种语言
  - definition.method: public function error(string $message, array $context = []): void {: 1 种语言
  - definition.method: public function error(string $message, array $context = []): void;: 1 种语言
  - definition.method: public function findAll(): array: 1 种语言
  - definition.method: public function findAll(): array;: 1 种语言
  - definition.method: public function findById(int $id): ?User: 1 种语言
  - definition.method: public function findById(int $id): ?User;: 1 种语言
  - definition.method: public function getAvatar(): ?string: 1 种语言
  - definition.method: public function getCreatedAt(): DateTime: 1 种语言
  - definition.method: public function getEmail(): string: 1 种语言
  - definition.method: public function getId(): int: 1 种语言
  - definition.method: public function getLabel(): string: 1 种语言
  - definition.method: public function getName(): string: 1 种语言
  - definition.method: public function getRoles(): array: 1 种语言
  - definition.method: public function getStatus(): UserStatus: 1 种语言
  - definition.method: public function getUpdatedAt(): DateTime: 1 种语言
  - definition.method: public function hasRole(string $role): bool: 1 种语言
  - definition.method: public function info(string $message, array $context = []): void {: 1 种语言
  - definition.method: public function info(string $message, array $context = []): void;: 1 种语言
  - definition.method: public function isActive(): bool: 1 种语言
  - definition.method: public function isValid(): bool: 1 种语言
  - definition.method: public function jsonSerialize(): array: 1 种语言
  - definition.method: public function removeRole(string $role): self: 1 种语言
  - definition.method: public function save(User $user): bool: 1 种语言
  - definition.method: public function save(User $user): bool;: 1 种语言
  - definition.method: public function setAvatar(?string $avatar): self: 1 种语言
  - definition.method: public function setEmail(string $email): self: 1 种语言
  - definition.method: public function setId(int $id): void: 1 种语言
  - definition.method: public function setName(string $name): self: 1 种语言
  - definition.method: public function setStatus(UserStatus $status): self: 1 种语言
  - definition.method: public function suspend(): self: 1 种语言
  - definition.method: public function toArray(): array: 1 种语言
  - definition.method: public function touch(): void: 1 种语言
  - definition.method: public function validate(): array: 1 种语言
  - definition.method: public static void staticNestedMethod() {: 1 种语言
  - definition.method: public void lambdaExamples() {: 1 种语言
  - definition.method: public void publicMethod() {: 1 种语言
  - definition.method: publicMethod() {: 1 种语言
  - definition.method: save(entity: T): Promise<T>;: 1 种语言
  - definition.method: set fullName(value) {: 1 种语言
  - definition.method: set nickname(value) {: 1 种语言
  - definition.method: size_t size() const { return data_.size(); }: 1 种语言
  - definition.method: static double distance(const Point& p1, const Point& p2) {: 1 种语言
  - definition.method: static func createSampleUser() -> User {: 1 种语言
  - definition.method: static getKingdom() {: 1 种语言
  - definition.method: static log(message: string): void {: 1 种语言
  - definition.method: static void staticInterfaceMethod() {: 1 种语言
  - definition.method: virtual void draw() const {: 1 种语言
  - definition.method: void add(bool value) {: 1 种语言
  - definition.method: void draw() const override {: 1 种语言
  - definition.method: void draw();: 1 种语言
  - definition.method: void setX(double x) { x_ = x; }: 1 种语言
  - definition.method: void setY(double y) { y_ = y; }: 1 种语言
  - definition.method_container: impl<T: Clone> Clone for Repository<T> {: 1 种语言
  - definition.method_container: impl<T> Repository<T> {: 1 种语言
  - definition.namespace: namespace Utils {: 1 种语言
  - definition.namespace: namespace geometry {: 1 种语言
  - definition.namespace: package com.example.sample: 1 种语言
  - definition.operator: Point operator+(const Point& other) const {: 1 种语言
  - definition.operator: bool operator==(const Point& other) const {: 1 种语言
  - definition.operator: bool operator[](size_t index) const { return data_[index]; }: 1 种语言
  - definition.struct: struct Repository<T> {: 1 种语言
  - definition.struct: struct User {: 1 种语言
  - definition.struct: struct UserService {: 1 种语言
  - definition.struct: typedef struct Node {: 1 种语言
  - definition.struct: typedef struct {: 1 种语言
  - definition.template: template<typename T>: 1 种语言
  - definition.union: typedef union {: 1 种语言
  - name.definition.function: func LogFields(msg string, fields ...interface{}) {: 1 种语言
  - name.definition.function: func Map[T, U any](slice []T, fn func(T) U) []U {: 1 种语言
  - name.definition.function: func NewUserService(repo UserRepository, logger Logger) *UserService {: 1 种语言
  - name.definition.function: func SafeOperation() (err error) {: 1 种语言
  - name.definition.function: func Sum[T Numeric](values []T) T {: 1 种语言
  - name.definition.function: func init() {: 1 种语言
  - name.definition.function: func main() {: 1 种语言
  - name.definition.function: func riskyOperation() {: 1 种语言
  - name.definition.method: func (s *UserService) CreateUser(ctx context.Context, user *User) error {: 1 种语言
  - name.definition.method: func (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {: 1 种语言
  - name.definition.method: func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {: 1 种语言
  - name.definition.method: func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {: 1 种语言
  - name.definition.method: func (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {: 1 种语言
  - name.definition.method: func (s *UserService) validateUser(user *User) error {: 1 种语言
  - name.definition.method: public double getArea() {: 1 种语言
  - name.definition.method: public void draw() {: 1 种语言
