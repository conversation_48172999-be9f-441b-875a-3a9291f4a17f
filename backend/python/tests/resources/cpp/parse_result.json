{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "filename": "sample.cpp", "content_length": 7645, "line_count": 282}, "parsing_results": {"key_structure_lines": {"13": "definition.namespace: namespace geometry {", "16": "definition.class: class Point {", "22": "definition.constructor: Point() : x_(0.0), y_(0.0) {}", "23": "definition.constructor: Point(double x, double y) : x_(x), y_(y) {}", "24": "definition.constructor: Point(const Point& other) : x_(other.x_), y_(other.y_) {}", "27": "definition.destructor: ~Point() = default;", "39": "definition.constructor: Point(Point&& other) noexcept : x_(other.x_), y_(other.y_) {", "56": "definition.method: double getX() const { return x_; }", "57": "definition.method: double getY() const { return y_; }", "60": "definition.method: void setX(double x) { x_ = x; }", "61": "definition.method: void setY(double y) { y_ = y; }", "64": "definition.operator: Point operator+(const Point& other) const {", "74": "definition.operator: bool operator==(const Point& other) const {", "85": "definition.method: static double distance(const Point& p1, const Point& p2) {", "93": "definition.class: class Shape {", "98": "definition.constructor: Shape(const std::string& color) : color_(color) {}", "99": "definition.destructor: virtual ~Shape() = default;", "106": "definition.method: virtual void draw() const {", "115": "definition.class: class Circle : public Shape {", "121": "definition.constructor: Circle(const Point& center, double radius, const std::string& color)", "125": "definition.method: double area() const override {", "129": "definition.method: double perimeter() const override {", "133": "definition.method: void draw() const override {", "140": "definition.method: double getRadius() const { return radius_; }", "146": "definition.template: template<typename T>", "147": "definition.class: class Container {", "153": "definition.constructor: Container() = default;", "154": "definition.constructor: Container(std::initializer_list<T> init) : data_(init) {}", "158": "definition.constructor: void add(U&& item) {", "163": "definition.method: auto begin() { return data_.begin(); }", "164": "definition.method: auto end() { return data_.end(); }", "165": "definition.method: auto begin() const { return data_.begin(); }", "166": "definition.method: auto end() const { return data_.end(); }", "169": "definition.method: size_t size() const { return data_.size(); }", "175": "definition.constructor: auto filter(Predicate pred) const {", "190": "definition.method: void add(bool value) {", "194": "definition.method: size_t size() const { return data_.size(); }", "196": "definition.operator: bool operator[](size_t index) const { return data_[index]; }", "201": "definition.constructor: T max(const T& a, const T& b) {", "206": "definition.constructor: auto add(const T& a, const U& b) -> decltype(a + b) {", "212": "definition.constructor: void print(Args... args) {", "218": "definition.constructor: void modernCppFeatures() {", "257": "definition.constructor: int main() {"}, "key_structure_count": 43, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 13, "end_line": 143, "content": "namespace geometry {\n    \n    // Class declaration\n    class Point {\n    private:\n        double x_, y_;\n        \n    public:\n        // Constructors\n        Point() : x_(0.0), y_(0.0) {}\n        Point(double x, double y) : x_(x), y_(y) {}\n        Point(const Point& other) : x_(other.x_), y_(other.y_) {}\n        \n        // Destructor\n        ~Point() = default;\n        \n        // Assignment operator\n        Point& operator=(const Point& other) {\n            if (this != &other) {\n                x_ = other.x_;\n                y_ = other.y_;\n            }\n            return *this;\n        }\n        \n        // Move constructor\n        Point(Point&& other) noexcept : x_(other.x_), y_(other.y_) {\n            other.x_ = 0.0;\n            other.y_ = 0.0;\n        }\n        \n        // Move assignment operator\n        Point& operator=(Point&& other) noexcept {\n            if (this != &other) {\n                x_ = other.x_;\n                y_ = other.y_;\n                other.x_ = 0.0;\n                other.y_ = 0.0;\n            }\n            return *this;\n        }\n        \n        // Getters\n        double getX() const { return x_; }\n        double getY() const { return y_; }\n        \n        // Setters\n        void setX(double x) { x_ = x; }\n        void setY(double y) { y_ = y; }\n        \n        // Operator overloading\n        Point operator+(const Point& other) const {\n            return Point(x_ + other.x_, y_ + other.y_);\n        }\n        \n        Point& operator+=(const Point& other) {\n            x_ += other.x_;\n            y_ += other.y_;\n            return *this;\n        }\n        \n        bool operator==(const Point& other) const {\n            return x_ == other.x_ && y_ == other.y_;\n        }\n        \n        // Friend function\n        friend std::ostream& operator<<(std::ostream& os, const Point& p) {\n            os << \"(\" << p.x_ << \", \" << p.y_ << \")\";\n            return os;\n        }\n        \n        // Static method\n        static double distance(const Point& p1, const Point& p2) {\n            double dx = p1.x_ - p2.x_;\n            double dy = p1.y_ - p2.y_;\n            return std::sqrt(dx * dx + dy * dy);\n        }\n    };\n    \n    // Abstract base class\n    class Shape {\n    protected:\n        std::string color_;\n        \n    public:\n        Shape(const std::string& color) : color_(color) {}\n        virtual ~Shape() = default;\n        \n        // Pure virtual functions\n        virtual double area() const = 0;\n        virtual double perimeter() const = 0;\n        \n        // Virtual function\n        virtual void draw() const {\n            std::cout << \"Drawing a \" << color_ << \" shape\" << std::endl;\n        }\n        \n        // Getter\n        const std::string& getColor() const { return color_; }\n    };\n    \n    // Derived class\n    class Circle : public Shape {\n    private:\n        Point center_;\n        double radius_;\n        \n    public:\n        Circle(const Point& center, double radius, const std::string& color)\n            : Shape(color), center_(center), radius_(radius) {}\n        \n        // Override virtual functions\n        double area() const override {\n            return 3.14159 * radius_ * radius_;\n        }\n        \n        double perimeter() const override {\n            return 2 * 3.14159 * radius_;\n        }\n        \n        void draw() const override {\n            std::cout << \"Drawing a \" << color_ << \" circle at \" << center_ \n                      << \" with radius \" << radius_ << std::endl;\n        }\n        \n        // Getters\n        const Point& getCenter() const { return center_; }\n        double getRadius() const { return radius_; }\n    };\n    \n} // namespace geometry", "line_count": 131}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 13, "end_line": 113, "content": "namespace geometry {\n    \n    // Class declaration\n    class Point {\n    private:\n        double x_, y_;\n        \n    public:\n        // Constructors\n        Point() : x_(0.0), y_(0.0) {}\n        Point(double x, double y) : x_(x), y_(y) {}\n        Point(const Point& other) : x_(other.x_), y_(other.y_) {}\n        \n        // Destructor\n        ~Point() = default;\n        \n        // Assignment operator\n        Point& operator=(const Point& other) {\n            if (this != &other) {\n                x_ = other.x_;\n                y_ = other.y_;\n            }\n            return *this;\n        }\n        \n        // Move constructor\n        Point(Point&& other) noexcept : x_(other.x_), y_(other.y_) {\n            other.x_ = 0.0;\n            other.y_ = 0.0;\n        }\n        \n        // Move assignment operator\n        Point& operator=(Point&& other) noexcept {\n            if (this != &other) {\n                x_ = other.x_;\n                y_ = other.y_;\n                other.x_ = 0.0;\n                other.y_ = 0.0;\n            }\n            return *this;\n        }\n        \n        // Getters\n        double getX() const { return x_; }\n        double getY() const { return y_; }\n        \n        // Setters\n        void setX(double x) { x_ = x; }\n        void setY(double y) { y_ = y; }\n        \n        // Operator overloading\n        Point operator+(const Point& other) const {\n            return Point(x_ + other.x_, y_ + other.y_);\n        }\n        \n        Point& operator+=(const Point& other) {\n            x_ += other.x_;\n            y_ += other.y_;\n            return *this;\n        }\n        \n        bool operator==(const Point& other) const {\n            return x_ == other.x_ && y_ == other.y_;\n        }\n        \n        // Friend function\n        friend std::ostream& operator<<(std::ostream& os, const Point& p) {\n            os << \"(\" << p.x_ << \", \" << p.y_ << \")\";\n            return os;\n        }\n        \n        // Static method\n        static double distance(const Point& p1, const Point& p2) {\n            double dx = p1.x_ - p2.x_;\n            double dy = p1.y_ - p2.y_;\n            return std::sqrt(dx * dx + dy * dy);\n        }\n    };\n    \n    // Abstract base class\n    class Shape {\n    protected:\n        std::string color_;\n        \n    public:\n        Shape(const std::string& color) : color_(color) {}\n        virtual ~Shape() = default;\n        \n        // Pure virtual functions\n        virtual double area() const = 0;\n        virtual double perimeter() const = 0;\n        \n        // Virtual function\n        virtual void draw() const {\n            std::cout << \"Drawing a \" << color_ << \" shape\" << std::endl;\n        }\n        \n        // Getter\n        const std::string& getColor() const { return color_; }\n    };\n    \n...\n} // namespace geometry", "line_count": 101}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 113, "end_line": 143, "content": "namespace geometry {\n...\n    \n    // Derived class\n    class Circle : public Shape {\n    private:\n        Point center_;\n        double radius_;\n        \n    public:\n        Circle(const Point& center, double radius, const std::string& color)\n            : Shape(color), center_(center), radius_(radius) {}\n        \n        // Override virtual functions\n        double area() const override {\n            return 3.14159 * radius_ * radius_;\n        }\n        \n        double perimeter() const override {\n            return 2 * 3.14159 * radius_;\n        }\n        \n        void draw() const override {\n            std::cout << \"Drawing a \" << color_ << \" circle at \" << center_ \n                      << \" with radius \" << radius_ << std::endl;\n        }\n        \n        // Getters\n        const Point& getCenter() const { return center_; }\n        double getRadius() const { return radius_; }\n    };\n    \n} // namespace geometry", "line_count": 31}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 146, "end_line": 196, "content": "template<typename T>\nclass Container {\nprivate:\n    std::vector<T> data_;\n    \npublic:\n    // Constructor\n    Container() = default;\n    Container(std::initializer_list<T> init) : data_(init) {}\n    \n    // Template member function\n    template<typename U>\n    void add(U&& item) {\n        data_.emplace_back(std::forward<U>(item));\n    }\n    \n    // Iterator support\n    auto begin() { return data_.begin(); }\n    auto end() { return data_.end(); }\n    auto begin() const { return data_.begin(); }\n    auto end() const { return data_.end(); }\n    \n    // Size and access\n    size_t size() const { return data_.size(); }\n    T& operator[](size_t index) { return data_[index]; }\n    const T& operator[](size_t index) const { return data_[index]; }\n    \n    // Template method with constraints (C++20)\n    template<typename Predicate>\n    auto filter(Predicate pred) const {\n        Container<T> result;\n        std::copy_if(data_.begin(), data_.end(), \n                     std::back_inserter(result.data_), pred);\n        return result;\n    }\n};\n\n// Template specialization\ntemplate<>\nclass Container<bool> {\nprivate:\n    std::vector<bool> data_;\n    \npublic:\n    void add(bool value) {\n        data_.push_back(value);\n    }\n    \n    size_t size() const { return data_.size(); }\n    \n    bool operator[](size_t index) const { return data_[index]; }", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 201, "end_line": 254, "content": "T max(const T& a, const T& b) {\n    return (a > b) ? a : b;\n}\n\ntemplate<typename T, typename U>\nauto add(const T& a, const U& b) -> decltype(a + b) {\n    return a + b;\n}\n\n// Variadic template\ntemplate<typename... Args>\nvoid print(Args... args) {\n    ((std::cout << args << \" \"), ...);\n    std::cout << std::endl;\n}\n\n// Lambda expressions and modern C++ features\nvoid modernCppFeatures() {\n    // Lambda expressions\n    auto lambda1 = [](int x) { return x * x; };\n    auto lambda2 = [](const auto& a, const auto& b) { return a + b; };\n    \n    // Capture by value and reference\n    int multiplier = 10;\n    auto lambda3 = [multiplier](int x) { return x * multiplier; };\n    auto lambda4 = [&multiplier](int x) { multiplier += x; return multiplier; };\n    \n    // Generic lambda (C++14)\n    auto generic_lambda = [](auto x, auto y) { return x + y; };\n    \n    // Smart pointers\n    auto unique_ptr = std::make_unique<geometry::Point>(1.0, 2.0);\n    auto shared_ptr = std::make_shared<geometry::Circle>(*unique_ptr, 5.0, \"red\");\n    \n    // Range-based for loop\n    std::vector<int> numbers = {1, 2, 3, 4, 5};\n    for (const auto& num : numbers) {\n        std::cout << num << \" \";\n    }\n    std::cout << std::endl;\n    \n    // Auto type deduction\n    auto result = add(3.14, 2);\n    \n    // Structured bindings (C++17)\n    auto [x, y] = std::make_pair(10, 20);\n    \n    // std::function\n    std::function<int(int, int)> operation = [](int a, int b) { return a + b; };\n    \n    // Algorithm usage\n    std::transform(numbers.begin(), numbers.end(), numbers.begin(),\n                   [](int x) { return x * 2; });\n}", "line_count": 54}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/cpp/sample.cpp", "start_line": 257, "end_line": 281, "content": "int main() {\n    using namespace geometry;\n    \n    // Object creation\n    Point p1(1.0, 2.0);\n    Point p2(3.0, 4.0);\n    \n    // Method calls\n    Point p3 = p1 + p2;\n    std::cout << \"p1 + p2 = \" << p3 << std::endl;\n    \n    // Polymorphism\n    std::unique_ptr<Shape> shape = std::make_unique<Circle>(p1, 5.0, \"blue\");\n    shape->draw();\n    std::cout << \"Area: \" << shape->area() << std::endl;\n    \n    // Template usage\n    Container<int> int_container{1, 2, 3, 4, 5};\n    Container<std::string> string_container{\"hello\", \"world\"};\n    \n    // Modern C++ features\n    modernCppFeatures();\n    \n    return 0;\n}", "line_count": 25}], "chunk_count": 6}, "analysis": {"detected_structures": ["definition.operator: Point operator+(const Point& other) const {", "definition.operator: bool operator==(const Point& other) const {", "definition.method: double getRadius() const { return radius_; }", "definition.constructor: Point(double x, double y) : x_(x), y_(y) {}", "definition.method: void draw() const override {", "definition.template: template<typename T>", "definition.method: auto end() { return data_.end(); }", "definition.constructor: Point() : x_(0.0), y_(0.0) {}", "definition.constructor: Point(const Point& other) : x_(other.x_), y_(other.y_) {}", "definition.method: double area() const override {", "definition.constructor: auto add(const T& a, const U& b) -> decltype(a + b) {", "definition.constructor: Point(Point&& other) noexcept : x_(other.x_), y_(other.y_) {", "definition.constructor: Circle(const Point& center, double radius, const std::string& color)", "definition.destructor: virtual ~Shape() = default;", "definition.method: void setY(double y) { y_ = y; }", "definition.method: auto end() const { return data_.end(); }", "definition.constructor: auto filter(Predicate pred) const {", "definition.constructor: void add(U&& item) {", "definition.method: void setX(double x) { x_ = x; }", "definition.class: class Circle : public Shape {", "definition.method: double getX() const { return x_; }", "definition.method: size_t size() const { return data_.size(); }", "definition.constructor: T max(const T& a, const T& b) {", "definition.operator: bool operator[](size_t index) const { return data_[index]; }", "definition.method: virtual void draw() const {", "definition.method: double perimeter() const override {", "definition.constructor: Container() = default;", "definition.method: auto begin() const { return data_.begin(); }", "definition.constructor: Shape(const std::string& color) : color_(color) {}", "definition.destructor: ~Point() = default;", "definition.class: class Shape {", "definition.method: void add(bool value) {", "definition.constructor: void modernCppFeatures() {", "definition.constructor: int main() {", "definition.constructor: Container(std::initializer_list<T> init) : data_(init) {}", "definition.method: static double distance(const Point& p1, const Point& p2) {", "definition.constructor: void print(Args... args) {", "definition.class: class Container {", "definition.class: class Point {", "definition.method: auto begin() { return data_.begin(); }", "definition.namespace: namespace geometry {", "definition.method: double getY() const { return y_; }"], "structure_types_count": 42, "total_lines_in_chunks": 393, "coverage_percentage": 139.36170212765958}}