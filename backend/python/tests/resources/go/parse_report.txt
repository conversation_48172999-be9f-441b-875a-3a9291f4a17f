FileParser 解析结果报告 - GO
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/go/sample.go
  文件名: sample.go
  内容长度: 6803 字符
  行数: 313

关键结构行 (14 个):
  第  68 行: name.definition.function: func NewUserService(repo UserRepository, logger Logger) *UserService {
           内容: func NewUserService(repo UserRepository, logger Logger) *UserService {
  第  77 行: name.definition.method: func (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {
           内容: func (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {
  第  99 行: name.definition.method: func (s *UserService) CreateUser(ctx context.Context, user *User) error {
           内容: func (s *UserService) CreateUser(ctx context.Context, user *User) error {
  第 125 行: name.definition.method: func (s *UserService) validateUser(user *User) error {
           内容: func (s *UserService) validateUser(user *User) error {
  第 136 行: name.definition.method: func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {
           内容: func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {
  第 142 行: name.definition.function: func Map[T, U any](slice []T, fn func(T) U) []U {
           内容: func Map[T, U any](slice []T, fn func(T) U) []U {
  第 158 行: name.definition.function: func Sum[T Numeric](values []T) T {
           内容: func Sum[T Numeric](values []T) T {
  第 167 行: name.definition.method: func (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {
           内容: func (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {
  第 205 行: name.definition.method: func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {
           内容: func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {
  第 236 行: name.definition.function: func LogFields(msg string, fields ...interface{}) {
           内容: func LogFields(msg string, fields ...interface{}) {
  第 241 行: name.definition.function: func SafeOperation() (err error) {
           内容: func SafeOperation() (err error) {
  第 253 行: name.definition.function: func riskyOperation() {
           内容: func riskyOperation() {
  第 259 行: name.definition.function: func init() {
           内容: func init() {
  第 264 行: name.definition.function: func main() {
           内容: func main() {

检测到的结构类型:
  - name.definition.function: func LogFields(msg string, fields ...interface{}) {: 1 个
  - name.definition.function: func Map[T, U any](slice []T, fn func(T) U) []U {: 1 个
  - name.definition.function: func NewUserService(repo UserRepository, logger Logger) *UserService {: 1 个
  - name.definition.function: func SafeOperation() (err error) {: 1 个
  - name.definition.function: func Sum[T Numeric](values []T) T {: 1 个
  - name.definition.function: func init() {: 1 个
  - name.definition.function: func main() {: 1 个
  - name.definition.function: func riskyOperation() {: 1 个
  - name.definition.method: func (s *UserService) CreateUser(ctx context.Context, user *User) error {: 1 个
  - name.definition.method: func (s *UserService) GetUser(ctx context.Context, id UserID) (*User, error) {: 1 个
  - name.definition.method: func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {: 1 个
  - name.definition.method: func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {: 1 个
  - name.definition.method: func (s *UserService) ProcessUsersAsync(ctx context.Context, userIDs []UserID) <-chan *User {: 1 个
  - name.definition.method: func (s *UserService) validateUser(user *User) error {: 1 个

代码块信息 (5 个):
  块 1: 第 3-54 行 (52 行)
      3: package main
      4: 
      5: import (
    ... (还有 49 行)

  块 2: 第 60-125 行 (66 行)
     60: type UserService struct {
     61: 	repo   UserRepository
     62: 	logger Logger
    ... (还有 63 行)

  块 3: 第 136-199 行 (64 行)
    136: func (s *UserService) GetUserStats(ctx context.Context) (total int, active int, err error) {
    137: 	// Implementation would go here
    138: 	return 100, 75, nil
    ... (还有 61 行)

  块 4: 第 205-259 行 (55 行)
    205: func (s *UserService) HandleGetUser(w http.ResponseWriter, r *http.Request) {
    206: 	ctx := r.Context()
    207: 	
    ... (还有 52 行)

  块 5: 第 264-269 行 (6 行)
    264: func main() {
    265: 	ctx := context.Background()
    266: 	
    ... (还有 3 行)


统计信息:
  覆盖率: 77.6%
  块中总行数: 243
  结构类型数: 14
