FileParser 解析结果报告 - JAVASCRIPT
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/javascript/sample.js
  文件名: sample.js
  内容长度: 4612 字符
  行数: 220

关键结构行 (29 个):
  第  20 行: definition.function: function regularFunction(param1, param2) {
           内容: function regularFunction(param1, param2) {
  第  25 行: definition.function: const arrowFunction = (x, y) => x * y;
           内容: const arrowFunction = (x, y) => x * y;
  第  26 行: definition.function: const singleParamArrow = x => x * 2;
           内容: const singleParamArrow = x => x * 2;
  第  27 行: definition.function: const noParamArrow = () => 'no params';
           内容: const noParamArrow = () => 'no params';
  第  30 行: definition.function: async function fetchData(url) {
           内容: async function fetchData(url) {
  第  40 行: definition.function: const asyncArrowFunction = async (id) => {
           内容: const asyncArrowFunction = async (id) => {
  第  46 行: definition.function: function* numberGenerator(max) {
           内容: function* numberGenerator(max) {
  第  65 行: definition.method: greet() {
           内容: greet() {
  第  70 行: definition.method: get displayName() {
           内容: get displayName() {
  第  75 行: definition.method: set fullName(value) {
           内容: set fullName(value) {
  第  82 行: definition.class: class Animal {
           内容: class Animal {
  第  89 行: definition.method: makeSound() {
           内容: makeSound() {
  第  94 行: definition.method: static getKingdom() {
           内容: static getKingdom() {
  第  99 行: definition.method: get description() {
           内容: get description() {
  第 104 行: definition.method: set nickname(value) {
           内容: set nickname(value) {
  第 110 行: definition.class: class Dog extends Animal {
           内容: class Dog extends Animal {
  第 116 行: definition.method: makeSound() {
           内容: makeSound() {
  第 128 行: definition.method: getPrivateData() {
           内容: getPrivateData() {
  第 134 行: definition.function: const functionExpression = function(x) {
           内容: const functionExpression = function(x) {
  第 138 行: definition.function: const namedFunctionExpression = function square(x) {
           内容: const namedFunctionExpression = function square(x) {
  第 148 行: definition.function: const higherOrderFunction = (callback) => {
           内容: const higherOrderFunction = (callback) => {
  第 164 行: definition.function: function sumAll(...args) {
           内容: function sumAll(...args) {
  第 169 行: definition.function: function greetUser(name = 'Guest', greeting = 'Hello') {
           内容: function greetUser(name = 'Guest', greeting = 'Hello') {
  第 174 行: definition.function: function processUser({ name = 'Unknown', age = 0, email = null } = {}) {
           内容: function processUser({ name = 'Unknown', age = 0, email = null } = {}) {
  第 195 行: definition.function: function privateFunction() {
           内容: function privateFunction() {
  第 200 行: definition.method: publicMethod() {
           内容: publicMethod() {
  第 204 行: definition.method: get counter() {
           内容: get counter() {
  第 211 行: definition.class: export default class DefaultExport {
           内容: export default class DefaultExport {
  第 216 行: definition.method: getValue() {
           内容: getValue() {

检测到的结构类型:
  - definition.class: class Animal {: 1 个
  - definition.class: class Dog extends Animal {: 1 个
  - definition.class: export default class DefaultExport {: 1 个
  - definition.function: async function fetchData(url) {: 1 个
  - definition.function: const arrowFunction = (x, y) => x * y;: 1 个
  - definition.function: const asyncArrowFunction = async (id) => {: 1 个
  - definition.function: const functionExpression = function(x) {: 1 个
  - definition.function: const higherOrderFunction = (callback) => {: 1 个
  - definition.function: const namedFunctionExpression = function square(x) {: 1 个
  - definition.function: const noParamArrow = () => 'no params';: 1 个
  - definition.function: const singleParamArrow = x => x * 2;: 1 个
  - definition.function: function greetUser(name = 'Guest', greeting = 'Hello') {: 1 个
  - definition.function: function privateFunction() {: 1 个
  - definition.function: function processUser({ name = 'Unknown', age = 0, email = null } = {}) {: 1 个
  - definition.function: function regularFunction(param1, param2) {: 1 个
  - definition.function: function sumAll(...args) {: 1 个
  - definition.function: function* numberGenerator(max) {: 1 个
  - definition.method: get counter() {: 1 个
  - definition.method: get description() {: 1 个
  - definition.method: get displayName() {: 1 个
  - definition.method: getPrivateData() {: 1 个
  - definition.method: getValue() {: 1 个
  - definition.method: greet() {: 1 个
  - definition.method: makeSound() {: 2 个
  - definition.method: publicMethod() {: 1 个
  - definition.method: set fullName(value) {: 1 个
  - definition.method: set nickname(value) {: 1 个
  - definition.method: static getKingdom() {: 1 个

代码块信息 (4 个):
  块 1: 第 20-79 行 (60 行)
     20: function regularFunction(param1, param2) {
     21:     return param1 + param2;
     22: }
    ... (还有 57 行)

  块 2: 第 82-136 行 (55 行)
     82: class Animal {
     83:     constructor(name, species) {
     84:         this.name = name;
    ... (还有 52 行)

  块 3: 第 138-197 行 (60 行)
    138: const namedFunctionExpression = function square(x) {
    139:     return x * x;
    140: };
    ... (还有 57 行)

  块 4: 第 199-219 行 (21 行)
    199:     return {
    200:         publicMethod() {
    201:             return privateFunction();
    ... (还有 18 行)


统计信息:
  覆盖率: 89.1%
  块中总行数: 196
  结构类型数: 28
