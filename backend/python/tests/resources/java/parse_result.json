{"file_info": {"path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/java/Sample.java", "filename": "Sample.java", "content_length": 4306, "line_count": 180}, "parsing_results": {"key_structure_lines": {"12": "definition.class: public class Sample {", "21": "definition.constructor: public Sample() {", "28": "definition.constructor: public Sample(int value, List<String> items) {", "35": "definition.method: public void publicMethod() {", "39": "definition.method: private int privateMethod(int a, int b) {", "43": "definition.method: protected static String staticMethod(String input) {", "48": "definition.method: public <T> List<T> genericMethod(T item, int count) {", "57": "definition.method: public void lambdaExamples() {", "68": "definition.class: public class InnerClass {", "71": "definition.constructor: public InnerClass(String value) {", "75": "definition.method: public String getInnerField() {", "81": "definition.class: public static class StaticNestedClass {", "84": "definition.method: public static void staticNestedMethod() {", "91": "definition.interface: interface Drawable {", "92": "definition.method: void draw();", "94": "definition.method: default void print() {", "98": "definition.method: static void staticInterfaceMethod() {", "104": "definition.enum: enum Color {", "111": "definition.constructor: Color(String description) {", "115": "definition.method: public String getDescription() {", "130": "definition.constructor: public Person(String name, int age) {", "135": "definition.method: public String getDisplayName() {", "147": "definition.class: abstract class Shape {", "150": "definition.constructor: public Shape(String color) {", "154": "definition.method: public abstract double getArea();", "156": "definition.method: public String getColor() {", "162": "definition.class: class Circle extends Shape implements Drawable {", "165": "definition.constructor: public Circle(String color, double radius) {", "170": "definition.method: @Override", "171": "name.definition.method: public double getArea() {", "175": "definition.method: @Override", "176": "name.definition.method: public void draw() {"}, "key_structure_count": 32, "chunks": [{"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/java/Sample.java", "start_line": 2, "end_line": 88, "content": "import java.util.*;\nimport java.util.function.Function;\nimport java.util.stream.Collectors;\n\n/**\n * Java sample file for testing FileParser functionality.\n * This file contains various Java syntax structures that should be captured by the tree-sitter queries.\n */\n\n// Line comment example\npublic class Sample {\n    \n    // Field declarations\n    private static final String CONSTANT = \"Hello World\";\n    private int instanceField;\n    protected List<String> protectedField;\n    public Map<String, Integer> publicField;\n    \n    // Constructor\n    public Sample() {\n        this.instanceField = 0;\n        this.protectedField = new ArrayList<>();\n        this.publicField = new HashMap<>();\n    }\n    \n    // Parameterized constructor\n    public Sample(int value, List<String> items) {\n        this.instanceField = value;\n        this.protectedField = new ArrayList<>(items);\n        this.publicField = new HashMap<>();\n    }\n    \n    // Method declarations\n    public void publicMethod() {\n        System.out.println(\"Public method called\");\n    }\n    \n    private int privateMethod(int a, int b) {\n        return a + b;\n    }\n    \n    protected static String staticMethod(String input) {\n        return input.toUpperCase();\n    }\n    \n    // Method with generic parameters\n    public <T> List<T> genericMethod(T item, int count) {\n        List<T> result = new ArrayList<>();\n        for (int i = 0; i < count; i++) {\n            result.add(item);\n        }\n        return result;\n    }\n    \n    // Lambda expressions\n    public void lambdaExamples() {\n        Function<String, Integer> stringLength = s -> s.length();\n        Function<Integer, Integer> square = x -> x * x;\n        \n        List<String> words = Arrays.asList(\"hello\", \"world\", \"java\");\n        List<Integer> lengths = words.stream()\n            .map(stringLength)\n            .collect(Collectors.toList());\n    }\n    \n    // Inner class\n    public class InnerClass {\n        private String innerField;\n        \n        public InnerClass(String value) {\n            this.innerField = value;\n        }\n        \n        public String getInnerField() {\n            return innerField;\n        }\n    }\n    \n    // Static nested class\n    public static class StaticNestedClass {\n        private static int staticNestedField = 100;\n        \n        public static void staticNestedMethod() {\n            System.out.println(\"Static nested method\");\n        }\n    }\n}", "line_count": 87}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/java/Sample.java", "start_line": 90, "end_line": 140, "content": "// Interface declaration\ninterface Drawable {\n    void draw();\n    \n    default void print() {\n        System.out.println(\"Drawing something\");\n    }\n    \n    static void staticInterfaceMethod() {\n        System.out.println(\"Static interface method\");\n    }\n}\n\n// Enum declaration\nenum Color {\n    RED(\"Red Color\"),\n    GREEN(\"Green Color\"),\n    <PERSON><PERSON>UE(\"Blue Color\");\n    \n    private final String description;\n    \n    Color(String description) {\n        this.description = description;\n    }\n    \n    public String getDescription() {\n        return description;\n    }\n}\n\n// Record declaration (Java 14+)\nrecord Person(String name, int age, String email) {\n    // Compact constructor\n    public Person {\n        if (age < 0) {\n            throw new IllegalArgumentException(\"Age cannot be negative\");\n        }\n    }\n    \n    // Additional constructor\n    public Person(String name, int age) {\n        this(name, age, null);\n    }\n    \n    // Instance method\n    public String getDisplayName() {\n        return name + \" (\" + age + \")\";\n    }\n}\n\n// Annotation declaration", "line_count": 51}, {"file_path": "/Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/java/Sample.java", "start_line": 141, "end_line": 179, "content": "@interface MyAnnotation {\n    String value() default \"\";\n    int priority() default 0;\n}\n\n// Abstract class\nabstract class Shape {\n    protected String color;\n    \n    public Shape(String color) {\n        this.color = color;\n    }\n    \n    public abstract double getArea();\n    \n    public String getColor() {\n        return color;\n    }\n}\n\n// Concrete class extending abstract class\nclass Circle extends Shape implements Drawable {\n    private double radius;\n    \n    public Circle(String color, double radius) {\n        super(color);\n        this.radius = radius;\n    }\n    \n    @Override\n    public double getArea() {\n        return Math.PI * radius * radius;\n    }\n    \n    @Override\n    public void draw() {\n        System.out.println(\"Drawing a \" + color + \" circle\");\n    }\n}", "line_count": 39}], "chunk_count": 3}, "analysis": {"detected_structures": ["definition.class: class Circle extends Shape implements Drawable {", "definition.method: @Override", "definition.method: public void publicMethod() {", "definition.class: public static class StaticNestedClass {", "name.definition.method: public double getArea() {", "definition.method: public String getDescription() {", "definition.method: public abstract double getArea();", "definition.enum: enum Color {", "definition.method: public static void staticNestedMethod() {", "definition.class: public class InnerClass {", "definition.constructor: public Person(String name, int age) {", "definition.method: static void staticInterfaceMethod() {", "definition.class: abstract class Shape {", "definition.method: protected static String staticMethod(String input) {", "definition.method: public String getInnerField() {", "definition.constructor: Color(String description) {", "definition.method: public String getColor() {", "definition.method: default void print() {", "definition.constructor: public Sample() {", "definition.method: public String getDisplayName() {", "definition.method: private int privateMethod(int a, int b) {", "definition.constructor: public Sample(int value, List<String> items) {", "definition.method: void draw();", "definition.constructor: public Circle(String color, double radius) {", "name.definition.method: public void draw() {", "definition.constructor: public Shape(String color) {", "definition.interface: interface Drawable {", "definition.method: public <T> List<T> genericMethod(T item, int count) {", "definition.class: public class Sample {", "definition.method: public void lambdaExamples() {", "definition.constructor: public InnerClass(String value) {"], "structure_types_count": 31, "total_lines_in_chunks": 177, "coverage_percentage": 98.33333333333333}}