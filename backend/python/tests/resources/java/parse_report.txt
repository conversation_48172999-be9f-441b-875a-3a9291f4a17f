FileParser 解析结果报告 - JAVA
============================================================

文件信息:
  路径: /Users/<USER>/01-Projects/Codebase-Dev/backend/python/tests/resources/java/Sample.java
  文件名: Sample.java
  内容长度: 4306 字符
  行数: 180

关键结构行 (32 个):
  第  12 行: definition.class: public class Sample {
           内容: public class Sample {
  第  21 行: definition.constructor: public Sample() {
           内容: public Sample() {
  第  28 行: definition.constructor: public Sample(int value, List<String> items) {
           内容: public Sample(int value, List<String> items) {
  第  35 行: definition.method: public void publicMethod() {
           内容: public void publicMethod() {
  第  39 行: definition.method: private int privateMethod(int a, int b) {
           内容: private int privateMethod(int a, int b) {
  第  43 行: definition.method: protected static String staticMethod(String input) {
           内容: protected static String staticMethod(String input) {
  第  48 行: definition.method: public <T> List<T> genericMethod(T item, int count) {
           内容: public <T> List<T> genericMethod(T item, int count) {
  第  57 行: definition.method: public void lambdaExamples() {
           内容: public void lambdaExamples() {
  第  68 行: definition.class: public class InnerClass {
           内容: public class InnerClass {
  第  71 行: definition.constructor: public InnerClass(String value) {
           内容: public InnerClass(String value) {
  第  75 行: definition.method: public String getInnerField() {
           内容: public String getInnerField() {
  第  81 行: definition.class: public static class StaticNestedClass {
           内容: public static class StaticNestedClass {
  第  84 行: definition.method: public static void staticNestedMethod() {
           内容: public static void staticNestedMethod() {
  第  91 行: definition.interface: interface Drawable {
           内容: interface Drawable {
  第  92 行: definition.method: void draw();
           内容: void draw();
  第  94 行: definition.method: default void print() {
           内容: default void print() {
  第  98 行: definition.method: static void staticInterfaceMethod() {
           内容: static void staticInterfaceMethod() {
  第 104 行: definition.enum: enum Color {
           内容: enum Color {
  第 111 行: definition.constructor: Color(String description) {
           内容: Color(String description) {
  第 115 行: definition.method: public String getDescription() {
           内容: public String getDescription() {
  第 130 行: definition.constructor: public Person(String name, int age) {
           内容: public Person(String name, int age) {
  第 135 行: definition.method: public String getDisplayName() {
           内容: public String getDisplayName() {
  第 147 行: definition.class: abstract class Shape {
           内容: abstract class Shape {
  第 150 行: definition.constructor: public Shape(String color) {
           内容: public Shape(String color) {
  第 154 行: definition.method: public abstract double getArea();
           内容: public abstract double getArea();
  第 156 行: definition.method: public String getColor() {
           内容: public String getColor() {
  第 162 行: definition.class: class Circle extends Shape implements Drawable {
           内容: class Circle extends Shape implements Drawable {
  第 165 行: definition.constructor: public Circle(String color, double radius) {
           内容: public Circle(String color, double radius) {
  第 170 行: definition.method: @Override
           内容: @Override
  第 171 行: name.definition.method: public double getArea() {
           内容: public double getArea() {
  第 175 行: definition.method: @Override
           内容: @Override
  第 176 行: name.definition.method: public void draw() {
           内容: public void draw() {

检测到的结构类型:
  - definition.class: abstract class Shape {: 1 个
  - definition.class: class Circle extends Shape implements Drawable {: 1 个
  - definition.class: public class InnerClass {: 1 个
  - definition.class: public class Sample {: 1 个
  - definition.class: public static class StaticNestedClass {: 1 个
  - definition.constructor: Color(String description) {: 1 个
  - definition.constructor: public Circle(String color, double radius) {: 1 个
  - definition.constructor: public InnerClass(String value) {: 1 个
  - definition.constructor: public Person(String name, int age) {: 1 个
  - definition.constructor: public Sample() {: 1 个
  - definition.constructor: public Sample(int value, List<String> items) {: 1 个
  - definition.constructor: public Shape(String color) {: 1 个
  - definition.enum: enum Color {: 1 个
  - definition.interface: interface Drawable {: 1 个
  - definition.method: @Override: 2 个
  - definition.method: default void print() {: 1 个
  - definition.method: private int privateMethod(int a, int b) {: 1 个
  - definition.method: protected static String staticMethod(String input) {: 1 个
  - definition.method: public <T> List<T> genericMethod(T item, int count) {: 1 个
  - definition.method: public String getColor() {: 1 个
  - definition.method: public String getDescription() {: 1 个
  - definition.method: public String getDisplayName() {: 1 个
  - definition.method: public String getInnerField() {: 1 个
  - definition.method: public abstract double getArea();: 1 个
  - definition.method: public static void staticNestedMethod() {: 1 个
  - definition.method: public void lambdaExamples() {: 1 个
  - definition.method: public void publicMethod() {: 1 个
  - definition.method: static void staticInterfaceMethod() {: 1 个
  - definition.method: void draw();: 1 个
  - name.definition.method: public double getArea() {: 1 个
  - name.definition.method: public void draw() {: 1 个

代码块信息 (3 个):
  块 1: 第 2-88 行 (87 行)
      2: import java.util.*;
      3: import java.util.function.Function;
      4: import java.util.stream.Collectors;
    ... (还有 84 行)

  块 2: 第 90-140 行 (51 行)
     90: // Interface declaration
     91: interface Drawable {
     92:     void draw();
    ... (还有 48 行)

  块 3: 第 141-179 行 (39 行)
    141: @interface MyAnnotation {
    142:     String value() default "";
    143:     int priority() default 0;
    ... (还有 36 行)


统计信息:
  覆盖率: 98.3%
  块中总行数: 177
  结构类型数: 31
